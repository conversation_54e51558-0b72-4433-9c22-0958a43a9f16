import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {
  Text,
  Card,
  useTheme,
  IconButton,
  Chip,
  Surface,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { LineChart } from 'react-native-chart-kit';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { apiClient } from '../services/api';
import { updatePrices } from '../store/slices/marketDataSlice';
import { colors } from '../utils/theme';

const { width } = Dimensions.get('window');

export default function DashboardScreen() {
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const dispatch = useAppDispatch();
  
  const { prices, watchedPairs } = useAppSelector((state) => state.marketData);
  const { user } = useAppSelector((state) => state.auth);
  const [refreshing, setRefreshing] = React.useState(false);

  const fetchPrices = async () => {
    try {
      const response = await apiClient.post('/market-data/prices', {
        pairs: watchedPairs,
      });
      dispatch(updatePrices(response.data.data));
    } catch (error) {
      console.error('Error fetching prices:', error);
    }
  };

  useEffect(() => {
    fetchPrices();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchPrices();
    setRefreshing(false);
  };

  const renderPriceCard = (pair: string) => {
    const price = prices[pair];
    if (!price) return null;

    const isPositive = price.changePercent >= 0;
    const changeColor = isPositive ? colors.green : colors.red;

    return (
      <Card
        key={pair}
        style={styles.priceCard}
        onPress={() => navigation.navigate('PairDetail', { pair })}
      >
        <Card.Content>
          <View style={styles.priceCardHeader}>
            <Text variant="titleMedium">{pair}</Text>
            <IconButton
              icon={isPositive ? 'trending-up' : 'trending-down'}
              iconColor={changeColor}
              size={20}
            />
          </View>
          <Text variant="headlineMedium" style={styles.priceText}>
            {price.bid.toFixed(5)}
          </Text>
          <View style={styles.priceChangeContainer}>
            <Chip
              compact
              style={[styles.changeChip, { backgroundColor: changeColor }]}
              textStyle={{ color: 'white' }}
            >
              {isPositive ? '+' : ''}{price.changePercent.toFixed(2)}%
            </Chip>
          </View>
        </Card.Content>
      </Card>
    );
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <Surface style={styles.welcomeCard}>
        <Text variant="headlineSmall">
          Welcome back, {user?.displayName || 'Trader'}!
        </Text>
        <Text variant="bodyMedium" style={styles.welcomeSubtext}>
          Here's your market overview
        </Text>
      </Surface>

      <Text variant="titleLarge" style={styles.sectionTitle}>
        Watched Pairs
      </Text>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.priceCardsContainer}
      >
        {watchedPairs.map(renderPriceCard)}
      </ScrollView>

      <Text variant="titleLarge" style={styles.sectionTitle}>
        Market Performance
      </Text>

      <Card style={styles.chartCard}>
        <Card.Content>
          <LineChart
            data={{
              labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
              datasets: [
                {
                  data: [1.1850, 1.1865, 1.1840, 1.1875, 1.1890],
                },
              ],
            }}
            width={width - 60}
            height={200}
            yAxisLabel=""
            yAxisSuffix=""
            chartConfig={{
              backgroundColor: theme.colors.surface,
              backgroundGradientFrom: theme.colors.surface,
              backgroundGradientTo: theme.colors.surface,
              decimalPlaces: 4,
              color: (opacity = 1) => theme.colors.primary,
              labelColor: (opacity = 1) => theme.colors.onSurface,
              style: {
                borderRadius: 16,
              },
              propsForDots: {
                r: '6',
                strokeWidth: '2',
                stroke: theme.colors.primary,
              },
            }}
            bezier
            style={styles.chart}
          />
        </Card.Content>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  welcomeCard: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    elevation: 2,
  },
  welcomeSubtext: {
    marginTop: 4,
    opacity: 0.7,
  },
  sectionTitle: {
    marginBottom: 16,
    marginTop: 8,
  },
  priceCardsContainer: {
    marginBottom: 24,
  },
  priceCard: {
    width: 160,
    marginRight: 12,
  },
  priceCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceText: {
    fontWeight: 'bold',
    marginVertical: 8,
  },
  priceChangeContainer: {
    flexDirection: 'row',
  },
  changeChip: {
    height: 24,
  },
  chartCard: {
    marginBottom: 24,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
});
