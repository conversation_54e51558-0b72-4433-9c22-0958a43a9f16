import request from 'supertest';
import { app } from '../../src/index';
import { marketDataService } from '../../src/services/marketData.service';
import { auth } from '../../src/config/firebase';

// Mock dependencies
jest.mock('../../src/services/marketData.service');
jest.mock('../../src/config/firebase', () => ({
  auth: jest.fn(() => ({
    verifyIdToken: jest.fn(),
  })),
}));

const mockedMarketDataService = marketDataService as jest.Mocked<typeof marketDataService>;
const mockedAuth = auth as jest.MockedFunction<typeof auth>;

describe('Market Data Routes Integration Tests', () => {
  const mockToken = 'valid-firebase-token';
  const mockUser = { uid: 'test-user-id', email: '<EMAIL>' };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock successful authentication
    (mockedAuth().verifyIdToken as jest.Mock).mockResolvedValue(mockUser);
  });

  describe('POST /api/market-data/prices', () => {
    const mockPricesData = {
      EURUSD: {
        symbol: 'EURUSD',
        bid: 1.0998,
        ask: 1.1002,
        timestamp: new Date(),
        change: 0.0005,
        changePercent: 0.05,
      },
      GBPUSD: {
        symbol: 'GBPUSD',
        bid: 1.2548,
        ask: 1.2552,
        timestamp: new Date(),
        change: -0.0010,
        changePercent: -0.08,
      },
    };

    it('should fetch multiple currency pair prices', async () => {
      // Arrange
      mockedMarketDataService.fetchForexData = jest.fn()
        .mockResolvedValueOnce(mockPricesData.EURUSD)
        .mockResolvedValueOnce(mockPricesData.GBPUSD);

      // Act
      const response = await request(app)
        .post('/api/market-data/prices')
        .set('Authorization', `Bearer ${mockToken}`)
        .send({ pairs: ['EURUSD', 'GBPUSD'] })
        .expect('Content-Type', /json/)
        .expect(200);

      // Assert
      expect(response.body.data).toHaveProperty('EURUSD');
      expect(response.body.data).toHaveProperty('GBPUSD');
      expect(mockedMarketDataService.fetchForexData).toHaveBeenCalledTimes(2);
      expect(mockedMarketDataService.fetchForexData).toHaveBeenCalledWith('EURUSD');
      expect(mockedMarketDataService.fetchForexData).toHaveBeenCalledWith('GBPUSD');
    });

    it('should handle empty pairs array', async () => {
      // Act
      const response = await request(app)
        .post('/api/market-data/prices')
        .set('Authorization', `Bearer ${mockToken}`)
        .send({ pairs: [] })
        .expect(200);

      // Assert
      expect(response.body.data).toEqual({});
      expect(mockedMarketDataService.fetchForexData).not.toHaveBeenCalled();
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      mockedMarketDataService.fetchForexData = jest.fn()
        .mockRejectedValue(new Error('Service unavailable'));

      // Act
      const response = await request(app)
        .post('/api/market-data/prices')
        .set('Authorization', `Bearer ${mockToken}`)
        .send({ pairs: ['EURUSD'] })
        .expect(200);

      // Assert
      expect(response.body.data.EURUSD).toBeNull();
    });

    it('should require authentication', async () => {
      // Act
      const response = await request(app)
        .post('/api/market-data/prices')
        .send({ pairs: ['EURUSD'] })
        .expect(401);

      // Assert
      expect(response.body.error).toBe('No token provided');
    });
  });

  describe('GET /api/market-data/history/:pair', () => {
    const mockHistoricalData = [
      {
        timestamp: new Date('2024-01-15T10:00:00Z'),
        open: 1.0990,
        high: 1.1010,
        low: 1.0985,
        close: 1.1005,
        volume: 1000000,
      },
      {
        timestamp: new Date('2024-01-15T11:00:00Z'),
        open: 1.1005,
        high: 1.1020,
        low: 1.1000,
        close: 1.1015,
        volume: 1200000,
      },
    ];

    it('should fetch historical data for a currency pair', async () => {
      // Arrange
      mockedMarketDataService.fetchHistoricalData = jest.fn()
        .mockResolvedValue(mockHistoricalData);

      // Act
      const response = await request(app)
        .get('/api/market-data/history/EURUSD')
        .set('Authorization', `Bearer ${mockToken}`)
        .query({ interval: '1h', outputsize: 'compact' })
        .expect(200);

      // Assert
      expect(response.body.data).toEqual(mockHistoricalData);
      expect(mockedMarketDataService.fetchHistoricalData).toHaveBeenCalledWith(
        'EURUSD',
        '1h'
      );
    });

    it('should use default parameters when not provided', async () => {
      // Arrange
      mockedMarketDataService.fetchHistoricalData = jest.fn()
        .mockResolvedValue(mockHistoricalData);

      // Act
      await request(app)
        .get('/api/market-data/history/EURUSD')
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(200);

      // Assert
      expect(mockedMarketDataService.fetchHistoricalData).toHaveBeenCalledWith(
        'EURUSD',
        '1D'
      );
    });

    it('should validate currency pair format', async () => {
      // Act
      const response = await request(app)
        .get('/api/market-data/history/INVALID')
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(400);

      // Assert
      expect(response.body.error).toContain('Invalid currency pair');
    });

    it('should handle service errors', async () => {
      // Arrange
      mockedMarketDataService.fetchHistoricalData = jest.fn()
        .mockRejectedValue(new Error('API limit reached'));

      // Act
      const response = await request(app)
        .get('/api/market-data/history/EURUSD')
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(500);

      // Assert
      expect(response.body.error).toBe('Failed to fetch historical data');
    });
  });

  describe('GET /api/market-data/supported-pairs', () => {
    it('should return list of supported currency pairs', async () => {
      // Act
      const response = await request(app)
        .get('/api/market-data/supported-pairs')
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(200);

      // Assert
      expect(response.body.pairs).toBeInstanceOf(Array);
      expect(response.body.pairs).toContain('EURUSD');
      expect(response.body.pairs).toContain('GBPUSD');
      expect(response.body.pairs).toContain('USDJPY');
    });
  });

  describe('WebSocket Price Updates', () => {
    it('should handle WebSocket connection for real-time prices', (done) => {
      // This would require a WebSocket client to test properly
      // For now, we'll just verify the endpoint exists
      request(app)
        .get('/api/market-data/ws')
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(426) // Upgrade Required
        .end((_err, res) => {
          expect(res.headers).toHaveProperty('upgrade', 'websocket');
          done();
        });
    });
  });
});

