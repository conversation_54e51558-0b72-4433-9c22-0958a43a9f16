import request from 'supertest';
import mongoose from 'mongoose';
import { app } from '../../src/index';
import { User } from '../../src/models/User';
import { Watchlist } from '../../src/models/Watchlist';
import jwt from 'jsonwebtoken';

describe('API Integration Tests', () => {
  let authToken: string;
  let userId: string;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/forex-test');
  });

  afterAll(async () => {
    // Clean up and disconnect
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Create test user
    const user = await User.create({
      email: '<EMAIL>',
      password: 'hashedPassword123',
      name: 'Test User'
    });
    userId = (user._id as any).toString();
    
    // Generate auth token
    authToken = jwt.sign(
      { id: userId, email: user.email },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterEach(async () => {
    // Clean up test data
    await User.deleteMany({});
    await Watchlist.deleteMany({});
  });

  describe('Watchlist API', () => {
    it('should create a new watchlist', async () => {
      const response = await request(app)
        .post('/api/v1/watchlist')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'My Favorites',
          symbols: ['EURUSD', 'GBPUSD']
        })
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe('My Favorites');
      expect(response.body.symbols).toHaveLength(2);
    });
  });
});

