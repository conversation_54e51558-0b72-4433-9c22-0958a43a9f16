import { calculateIndicators } from '../../src/services/analysis.service';
import { marketDataService } from '../../src/services/marketData.service';
import { logger } from '../../src/utils/logger';

// Mock dependencies
jest.mock('../../../src/services/marketData.service');
jest.mock('../../../src/utils/logger');

const mockedMarketDataService = marketDataService as jest.Mocked<typeof marketDataService>;
const mockedLogger = logger as jest.Mocked<typeof logger>;

describe('AnalysisService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Helper function to generate mock historical data
  const generateMockData = (count: number): any[] => {
    const data = [];
    let basePrice = 1.1000;
    
    for (let i = 0; i < count; i++) {
      const variation = (Math.random() - 0.5) * 0.01;
      basePrice += variation;
      data.push({
        timestamp: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        open: basePrice - 0.0005,
        high: basePrice + 0.0010,
        low: basePrice - 0.0010,
        close: basePrice,
        volume: Math.floor(Math.random() * 1000000),
      });
    }
    
    return data.reverse();
  };

  describe('calculateIndicators', () => {
    it('should calculate SMA indicator correctly', async () => {
      // Arrange
      const mockData = generateMockData(20);
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['sma'], 14);

      // Assert
      expect(mockedMarketDataService.getHistoricalData).toHaveBeenCalledWith(
        'EURUSD',
        'daily',
        'full'
      );
      expect(results).toHaveLength(1);
      expect(results[0].indicator).toBe('SMA');
      expect(typeof results[0].value).toBe('number');
    });

    it('should calculate EMA indicator correctly', async () => {
      // Arrange
      const mockData = generateMockData(20);
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['ema'], 14);

      // Assert
      expect(results).toHaveLength(1);
      expect(results[0].indicator).toBe('EMA');
      expect(typeof results[0].value).toBe('number');
    });

    it('should calculate RSI indicator with signal', async () => {
      // Arrange
      const mockData = generateMockData(30);
      // Manipulate data to create overbought condition
      mockData.slice(-5).forEach((d, i) => {
        d.close = 1.2000 + i * 0.001;
      });
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['rsi'], 14);

      // Assert
      expect(results).toHaveLength(1);
      expect(results[0].indicator).toBe('RSI');
      expect(typeof results[0].value).toBe('number');
      expect(results[0].signal).toBeDefined();
      expect(['Overbought', 'Oversold', 'Neutral']).toContain(results[0].signal);
    });

    it('should calculate MACD indicator correctly', async () => {
      // Arrange
      const mockData = generateMockData(50);
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['macd'], 14);

      // Assert
      expect(results).toHaveLength(1);
      expect(results[0].indicator).toBe('MACD');
      expect(Array.isArray(results[0].value)).toBe(true);
      expect(results[0].value).toHaveLength(3); // MACD, Signal, Histogram
    });

    it('should calculate multiple indicators', async () => {
      // Arrange
      const mockData = generateMockData(50);
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['sma', 'ema', 'rsi', 'macd'], 14);

      // Assert
      expect(results).toHaveLength(4);
      expect(results.map(r => r.indicator)).toEqual(['SMA', 'EMA', 'RSI', 'MACD']);
    });

    it('should throw error when insufficient data', async () => {
      // Arrange
      const mockData = generateMockData(5); // Less than required period
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act & Assert
      await expect(calculateIndicators('EURUSD', ['sma'], 14)).rejects.toThrow(
        'Insufficient data for calculations'
      );
    });

    it('should handle empty historical data', async () => {
      // Arrange
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue([]);

      // Act & Assert
      await expect(calculateIndicators('EURUSD', ['sma'], 14)).rejects.toThrow(
        'Insufficient data for calculations'
      );
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      const error = new Error('API Error');
      mockedMarketDataService.getHistoricalData = jest.fn().mockRejectedValue(error);

      // Act & Assert
      await expect(calculateIndicators('EURUSD', ['sma'], 14)).rejects.toThrow('API Error');
      expect(mockedLogger.error).toHaveBeenCalledWith(
        'Error calculating indicators:',
        error
      );
    });

    it('should use custom period parameter', async () => {
      // Arrange
      const mockData = generateMockData(30);
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['sma'], 20);

      // Assert
      expect(results[0].indicator).toBe('SMA');
      // The SMA should be calculated using the last 20 values
    });

    it('should handle case-insensitive indicator names', async () => {
      // Arrange
      const mockData = generateMockData(30);
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['SMA', 'EMA', 'RSI', 'MACD'], 14);

      // Assert
      expect(results).toHaveLength(4);
      expect(results.map(r => r.indicator)).toEqual(['SMA', 'EMA', 'RSI', 'MACD']);
    });
  });

  describe('RSI Signal Tests', () => {
    it('should return Overbought signal when RSI > 70', async () => {
      // Arrange
      const mockData = generateMockData(30);
      // Create strong upward trend for high RSI
      for (let i = 20; i < 30; i++) {
        mockData[i].close = 1.1000 + (i - 20) * 0.002;
      }
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['rsi'], 14);

      // Assert
      const rsiResult = results[0];
      if (rsiResult.value as number > 70) {
        expect(rsiResult.signal).toBe('Overbought');
      }
    });

    it('should return Oversold signal when RSI < 30', async () => {
      // Arrange
      const mockData = generateMockData(30);
      // Create strong downward trend for low RSI
      for (let i = 20; i < 30; i++) {
        mockData[i].close = 1.1000 - (i - 20) * 0.002;
      }
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['rsi'], 14);

      // Assert
      const rsiResult = results[0];
      if (rsiResult.value as number < 30) {
        expect(rsiResult.signal).toBe('Oversold');
      }
    });

    it('should return Neutral signal when RSI between 30 and 70', async () => {
      // Arrange
      const mockData = generateMockData(30);
      // Create sideways movement for neutral RSI
      mockData.forEach((d, i) => {
        d.close = 1.1000 + Math.sin(i * 0.2) * 0.001;
      });
      mockedMarketDataService.getHistoricalData = jest.fn().mockResolvedValue(mockData);

      // Act
      const results = await calculateIndicators('EURUSD', ['rsi'], 14);

      // Assert
      const rsiResult = results[0];
      const rsiValue = rsiResult.value as number;
      if (rsiValue >= 30 && rsiValue <= 70) {
        expect(rsiResult.signal).toBe('Neutral');
      }
    });
  });
});

