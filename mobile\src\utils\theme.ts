import { MD3DarkTheme, MD3LightTheme, configureFonts } from 'react-native-paper';
import { DefaultTheme, DarkTheme } from '@react-navigation/native';

const fontConfig = {
  web: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400' as const,
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500' as const,
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300' as const,
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100' as const,
    },
  },
  ios: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400' as const,
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500' as const,
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300' as const,
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100' as const,
    },
  },
  android: {
    regular: {
      fontFamily: 'sans-serif',
      fontWeight: 'normal' as const,
    },
    medium: {
      fontFamily: 'sans-serif-medium',
      fontWeight: 'normal' as const,
    },
    light: {
      fontFamily: 'sans-serif-light',
      fontWeight: 'normal' as const,
    },
    thin: {
      fontFamily: 'sans-serif-thin',
      fontWeight: 'normal' as const,
    },
  },
};
export const lightTheme = {
  ...MD3LightTheme,
  ...DefaultTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...DefaultTheme.colors,
    primary: '#4CAF50',
    secondary: '#2196F3',
    tertiary: '#FF9800',
    background: '#F5F5F5',
    surface: '#FFFFFF',
    error: '#F44336',
    onSurface: '#000000',
    onBackground: '#000000',
    success: '#4CAF50',
    warning: '#FF9800',
    info: '#2196F3',
    accent: '#9C27B0',
  },
  fonts: configureFonts({ config: fontConfig }),
};

export const darkTheme = {
  ...MD3DarkTheme,
  ...DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...DarkTheme.colors,
    primary: '#66BB6A',
    secondary: '#42A5F5',
    tertiary: '#FFA726',
    background: '#121212',
    surface: '#1E1E1E',
    error: '#EF5350',
    onSurface: '#FFFFFF',
    onBackground: '#FFFFFF',
    success: '#66BB6A',
    warning: '#FFA726',
    info: '#42A5F5',
    accent: '#AB47BC',
  },
  fonts: configureFonts({ config: fontConfig }),
};

export const colors = {
  green: '#4CAF50',
  red: '#F44336',
  blue: '#2196F3',
  orange: '#FF9800',
  purple: '#9C27B0',
  gray: '#9E9E9E',
  lightGray: '#E0E0E0',
  darkGray: '#424242',
};
