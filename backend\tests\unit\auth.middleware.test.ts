import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { auth } from '../../src/config/firebase';
import { authMiddleware } from '../../src/middleware/auth';

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('../../src/config/firebase', () => ({
  auth: jest.fn(() => ({
    verifyIdToken: jest.fn()
  }))
}));

describe('Auth Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction;

  beforeEach(() => {
    mockRequest = {
      headers: {},
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
    jest.clearAllMocks();
  });

  describe('JWT Token Validation', () => {
    it('should reject request without authorization header', async () => {
      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'No authorization header provided',
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should reject request with invalid Bearer format', async () => {
      // Arrange
      mockRequest.headers = {
        authorization: 'InvalidFormat token',
      };

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid authorization format',
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should validate and decode valid JWT token', async () => {
      // Arrange
      const mockUser = { id: 'user123', email: '<EMAIL>' };
      const mockToken = 'valid.jwt.token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`,
      };
      (jwt.verify as jest.Mock).mockReturnValue(mockUser);

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith(
        mockToken,
        process.env.JWT_SECRET
      );
      expect((mockRequest as any).user).toEqual(mockUser);
      expect(nextFunction).toHaveBeenCalled();
    });

    it('should reject expired JWT tokens', async () => {
      // Arrange
      const mockToken = 'expired.jwt.token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`,
      };
      (jwt.verify as jest.Mock).mockImplementation(() => {
        throw new jwt.TokenExpiredError('jwt expired', new Date());
      });

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Token expired',
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should reject malformed JWT tokens', async () => {
      // Arrange
      const mockToken = 'malformed.token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`,
      };
      (jwt.verify as jest.Mock).mockImplementation(() => {
        throw new jwt.JsonWebTokenError('invalid token');
      });

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid token',
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });
  });

  describe('Firebase Token Validation', () => {
    it('should validate Firebase ID tokens', async () => {
      // Arrange
      const mockToken = 'firebase.id.token';
      const mockDecodedToken = {
        uid: 'firebase-user-123',
        email: '<EMAIL>',
      };
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`,
        'x-auth-provider': 'firebase',
      };
      (auth().verifyIdToken as jest.Mock).mockResolvedValue(
        mockDecodedToken
      );

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(auth().verifyIdToken).toHaveBeenCalledWith(
        mockToken
      );
      expect((mockRequest as any).user).toEqual({
        id: mockDecodedToken.uid,
        email: mockDecodedToken.email,
        provider: 'firebase',
      });
      expect(nextFunction).toHaveBeenCalled();
    });

    it('should reject invalid Firebase tokens', async () => {
      // Arrange
      const mockToken = 'invalid.firebase.token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`,
        'x-auth-provider': 'firebase',
      };
      (auth().verifyIdToken as jest.Mock).mockRejectedValue(
        new Error('Firebase token validation failed')
      );

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Firebase authentication failed',
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });
  });

  describe('Rate Limiting Integration', () => {
    it('should track authenticated user requests for rate limiting', async () => {
      // Arrange
      const mockUser = { id: 'user123', email: '<EMAIL>' };
      const mockToken = 'valid.jwt.token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`,
      };
      mockRequest.ip = '***********';
      (jwt.verify as jest.Mock).mockReturnValue(mockUser);

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect((mockRequest as any).rateLimitKey).toBe(`user:${mockUser.id}`);
      expect(nextFunction).toHaveBeenCalled();
    });
  });
});
