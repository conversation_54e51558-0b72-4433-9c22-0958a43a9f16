import axios from 'axios';
import { logger } from '../../src/utils/logger';
import { cacheService } from '../../src/services/cache.service';
import NewsModel from '../../src/models/News';

// Import the news service class
const NewsService = require('../../src/services/news.service').NewsService;

// Mock dependencies
jest.mock('axios');
jest.mock('../../src/utils/logger');
jest.mock('../../src/services/cache.service');
jest.mock('../../src/models/News');

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedLogger = logger as jest.Mocked<typeof logger>;
const mockedCache = cacheService as jest.Mocked<typeof cacheService>;
const mockedNewsModel = NewsModel as jest.Mocked<typeof NewsModel>;

describe('NewsService', () => {
  let newsService: any;

  beforeEach(() => {
    jest.clearAllMocks();
    newsService = new NewsService();
    process.env.FINNHUB_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    delete process.env.FINNHUB_API_KEY;
  });

  describe('fetchEconomicCalendar', () => {
    const mockCalendarData = {
      economicCalendar: [
        {
          country: 'US',
          event: 'Non-Farm Payrolls',
          impact: 'high',
          actual: '200K',
          estimate: '180K',
          previous: '175K',
          time: '2024-01-15T13:30:00Z',
        },
        {
          country: 'EU',
          event: 'ECB Interest Rate Decision',
          impact: 'high',
          actual: '4.50%',
          estimate: '4.50%',
          previous: '4.50%',
          time: '2024-01-15T12:45:00Z',
        },
      ],
    };

    it('should fetch economic calendar from API when not cached', async () => {
      // Arrange
      mockedCache.get.mockResolvedValue(null);
      mockedAxios.get.mockResolvedValue({ data: mockCalendarData });

      // Act
      const result = await newsService.fetchEconomicCalendar();

      // Assert
      expect(mockedCache.get).toHaveBeenCalledWith('economic-calendar');
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://finnhub.io/api/v1/calendar/economic?token=test-api-key'
      );
      expect(mockedCache.set).toHaveBeenCalledWith(
        'economic-calendar',
        JSON.stringify(mockCalendarData.economicCalendar),
        3600
      );
      expect(result).toEqual(mockCalendarData.economicCalendar);
    });

    it('should return cached data when available', async () => {
      // Arrange
      const cachedData = JSON.stringify(mockCalendarData.economicCalendar);
      mockedCache.get.mockResolvedValue(cachedData);

      // Act
      const result = await newsService.fetchEconomicCalendar();

      // Assert
      expect(mockedCache.get).toHaveBeenCalledWith('economic-calendar');
      expect(mockedAxios.get).not.toHaveBeenCalled();
      expect(result).toEqual(mockCalendarData.economicCalendar);
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      mockedCache.get.mockResolvedValue(null);
      mockedAxios.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await newsService.fetchEconomicCalendar();

      // Assert
      expect(mockedLogger.error).toHaveBeenCalledWith(
        'Error fetching economic calendar:',
        expect.any(Error)
      );
      expect(result).toEqual([]);
    });

    it('should handle empty response data', async () => {
      // Arrange
      mockedCache.get.mockResolvedValue(null);
      mockedAxios.get.mockResolvedValue({ data: {} });

      // Act
      const result = await newsService.fetchEconomicCalendar();

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('fetchForexNews', () => {
    const mockNewsData = [
      {
        headline: 'EUR/USD Rises on ECB Decision',
        summary: 'The euro strengthened against the dollar...',
        url: 'https://example.com/news1',
        source: 'Reuters',
        datetime: 1642252800,
        image: 'https://example.com/image1.jpg',
      },
      {
        headline: 'GBP Weakens Amid Brexit Concerns',
        summary: 'Sterling fell to a new low...',
        url: 'https://example.com/news2',
        source: 'Bloomberg',
        datetime: 1642256400,
        image: 'https://example.com/image2.jpg',
      },
    ];

    beforeEach(() => {
      // Mock the saveNews method
      newsService.saveNews = jest.fn().mockResolvedValue(true);
    });

    it('should fetch forex news from API when not cached', async () => {
      // Arrange
      mockedCache.get.mockResolvedValue(null);
      mockedAxios.get.mockResolvedValue({ data: mockNewsData });

      // Act
      const result = await newsService.fetchForexNews();

      // Assert
      expect(mockedCache.get).toHaveBeenCalledWith('forex-news');
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://finnhub.io/api/v1/news?category=forex&token=test-api-key'
      );
      expect(newsService.saveNews).toHaveBeenCalledTimes(2);
      expect(mockedCache.set).toHaveBeenCalledWith(
        'forex-news',
        JSON.stringify(mockNewsData),
        1800
      );
      expect(result).toEqual(mockNewsData);
    });

    it('should return cached news when available', async () => {
      // Arrange
      const cachedData = JSON.stringify(mockNewsData);
      mockedCache.get.mockResolvedValue(cachedData);

      // Act
      const result = await newsService.fetchForexNews();

      // Assert
      expect(mockedCache.get).toHaveBeenCalledWith('forex-news');
      expect(mockedAxios.get).not.toHaveBeenCalled();
      expect(newsService.saveNews).not.toHaveBeenCalled();
      expect(result).toEqual(mockNewsData);
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      mockedCache.get.mockResolvedValue(null);
      mockedAxios.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await newsService.fetchForexNews();

      // Assert
      expect(mockedLogger.error).toHaveBeenCalledWith(
        'Error fetching forex news:',
        expect.any(Error)
      );
      expect(result).toEqual([]);
    });

    it('should handle saveNews errors gracefully', async () => {
      // Arrange
      mockedCache.get.mockResolvedValue(null);
      mockedAxios.get.mockResolvedValue({ data: mockNewsData });
      newsService.saveNews = jest.fn()
        .mockResolvedValueOnce(true)
        .mockRejectedValueOnce(new Error('DB Error'));

      // Act
      const result = await newsService.fetchForexNews();

      // Assert
      expect(newsService.saveNews).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockNewsData); // Should still return the news
    });
  });

  describe('getLatestNews', () => {
    const mockDbNews = [
      {
        _id: '1',
        title: 'Latest Forex Update',
        summary: 'Market analysis...',
        url: 'https://example.com/latest',
        source: 'FX Daily',
        publishedAt: new Date('2024-01-15T10:00:00Z'),
        createdAt: new Date('2024-01-15T10:05:00Z'),
      },
    ];

    it('should fetch latest news from database', async () => {
      // Arrange
      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDbNews),
      };
      mockedNewsModel.find = jest.fn().mockReturnValue(mockQuery);

      // Act
      const result = await newsService.getLatestNews(10);

      // Assert
      expect(mockedNewsModel.find).toHaveBeenCalledWith({});
      expect(mockQuery.sort).toHaveBeenCalledWith({ publishedAt: -1 });
      expect(mockQuery.limit).toHaveBeenCalledWith(10);
      expect(result).toEqual(mockDbNews);
    });

    it('should use default limit when not specified', async () => {
      // Arrange
      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDbNews),
      };
      mockedNewsModel.find = jest.fn().mockReturnValue(mockQuery);

      // Act
      await newsService.getLatestNews();

      // Assert
      expect(mockQuery.limit).toHaveBeenCalledWith(20);
    });

    it('should handle database errors', async () => {
      // Arrange
      const mockQuery = {
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockRejectedValue(new Error('DB Error')),
      };
      mockedNewsModel.find = jest.fn().mockReturnValue(mockQuery);

      // Act
      const result = await newsService.getLatestNews();

      // Assert
      expect(mockedLogger.error).toHaveBeenCalledWith(
        'Error fetching latest news:',
        expect.any(Error)
      );
      expect(result).toEqual([]);
    });
  });
});

