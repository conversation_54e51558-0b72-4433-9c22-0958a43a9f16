import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ForexPair } from '../../types';

interface MarketDataState {
  prices: Record<string, ForexPair>;
  watchedPairs: string[];
  historicalData: Record<string, any[]>;
  loading: boolean;
  error: string | null;
}

const initialState: MarketDataState = {
  prices: {},
  watchedPairs: ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD'],
  historicalData: {},
  loading: false,
  error: null,
};

export const marketDataSlice = createSlice({
  name: 'marketData',
  initialState,
  reducers: {
    updatePrices: (state, action: PayloadAction<ForexPair[]>) => {
      action.payload.forEach((pair) => {
        state.prices[pair.symbol] = pair;
      });
    },
    updateSinglePrice: (state, action: PayloadAction<ForexPair>) => {
      state.prices[action.payload.symbol] = action.payload;
    },
    addWatchedPair: (state, action: PayloadAction<string>) => {
      if (!state.watchedPairs.includes(action.payload)) {
        state.watchedPairs.push(action.payload);
      }
    },
    removeWatchedPair: (state, action: PayloadAction<string>) => {
      state.watchedPairs = state.watchedPairs.filter(
        (pair) => pair !== action.payload
      );
    },
    setHistoricalData: (
      state,
      action: PayloadAction<{ pair: string; data: any[] }>
    ) => {
      state.historicalData[action.payload.pair] = action.payload.data;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  updatePrices,
  updateSinglePrice,
  addWatchedPair,
  removeWatchedPair,
  setHistoricalData,
  setLoading,
  setError,
} = marketDataSlice.actions;

export default marketDataSlice.reducer;
