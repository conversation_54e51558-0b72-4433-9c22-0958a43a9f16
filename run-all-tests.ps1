# Forex Trading Platform - Test Execution Script (Windows)

Write-Host "🚀 Starting Forex Trading Platform Test Suite" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan

# Function to run tests
function Run-Test {
    param($TestName, $TestCommand, $Directory)
    
    Write-Host "`n🔍 Running $TestName..." -ForegroundColor Yellow
    Set-Location $Directory
    
    try {
        Invoke-Expression $TestCommand
        Write-Host "✅ $TestName passed" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ $TestName failed" -ForegroundColor Red
        return $false
    }
}

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

# Backend Tests
Write-Host "`n=== BACKEND TESTS ===" -ForegroundColor Yellow
if (-not (Test-Path "backend\node_modules")) {
    Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
    Set-Location backend
    npm install
}

Run-Test "Backend Tests" "npm test" "$ScriptDir\backend"

# Mobile Tests
Write-Host "`n=== MOBILE TESTS ===" -ForegroundColor Yellow
if (-not (Test-Path "mobile\node_modules")) {
    Write-Host "Installing mobile dependencies..." -ForegroundColor Yellow
    Set-Location mobile
    npm install
}

Run-Test "Mobile Tests" "npm test" "$ScriptDir\mobile"

Write-Host "`n✨ Test execution completed!" -ForegroundColor Green
