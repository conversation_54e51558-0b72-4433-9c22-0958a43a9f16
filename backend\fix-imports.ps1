# Fix all import paths in test files
$testPath = "C:\Users\<USER>\Desktop\forex-trading-platform\backend\tests"
$files = Get-ChildItem -Path $testPath -Recurse -Filter "*.ts"

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    $updatedContent = $content -replace "from '../../../src", "from '../../src"
    
    if ($content -ne $updatedContent) {
        Set-Content -Path $file.FullName -Value $updatedContent
        Write-Host "Fixed imports in: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Import paths fixed!" -ForegroundColor Cyan
