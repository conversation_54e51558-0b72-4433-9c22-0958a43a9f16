import { Router } from 'express';
import Watchlist from '../models/Watchlist';
import { auth } from '../config/firebase';
import { logger } from '../utils/logger';

const router = Router();

// Middleware to verify Firebase token
const verifyToken = async (req: any, res: any, next: any) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decodedToken = await auth().verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    logger.error('Token verification error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Get all watchlists for user
router.get('/', verifyToken, async (req: any, res) => {
  try {
    const watchlists = await Watchlist.find({ userId: req.user.uid });
    res.json({ watchlists });
  } catch (error) {
    logger.error('Get watchlists error:', error);
    res.status(500).json({ error: 'Failed to get watchlists' });
  }
});

// Create new watchlist
router.post('/', verifyToken, async (req: any, res) => {
  try {
    const { name, description, pairs = [] } = req.body;
    
    const watchlist = await Watchlist.create({
      userId: req.user.uid,
      name,
      description,
      pairs,
    });
    
    res.status(201).json({ watchlist });
  } catch (error) {
    logger.error('Create watchlist error:', error);
    res.status(500).json({ error: 'Failed to create watchlist' });
  }
});

// Update watchlist
router.put('/:id', verifyToken, async (req: any, res) => {
  try {
    const { id } = req.params;
    const { name, description, pairs, alerts } = req.body;
    
    const watchlist = await Watchlist.findOneAndUpdate(
      { _id: id, userId: req.user.uid },
      { name, description, pairs, alerts },
      { new: true }
    );
    
    if (!watchlist) {
      return res.status(404).json({ error: 'Watchlist not found' });
    }
    
    res.json({ watchlist });
  } catch (error) {
    logger.error('Update watchlist error:', error);
    res.status(500).json({ error: 'Failed to update watchlist' });
  }
});

// Delete watchlist
router.delete('/:id', verifyToken, async (req: any, res) => {
  try {
    const { id } = req.params;
    
    const watchlist = await Watchlist.findOneAndDelete({
      _id: id,
      userId: req.user.uid,
    });
    
    if (!watchlist) {
      return res.status(404).json({ error: 'Watchlist not found' });
    }
    
    res.json({ message: 'Watchlist deleted successfully' });
  } catch (error) {
    logger.error('Delete watchlist error:', error);
    res.status(500).json({ error: 'Failed to delete watchlist' });
  }
});

// Add alert to watchlist
router.post('/:id/alerts', verifyToken, async (req: any, res) => {
  try {
    const { id } = req.params;
    const { pair, type, value } = req.body;
    
    const watchlist = await Watchlist.findOne({
      _id: id,
      userId: req.user.uid,
    });
    
    if (!watchlist) {
      return res.status(404).json({ error: 'Watchlist not found' });
    }
    
    watchlist.alerts.push({
      pair,
      type,
      value,
      enabled: true,
    });
    
    await watchlist.save();
    res.json({ watchlist });
  } catch (error) {
    logger.error('Add alert error:', error);
    res.status(500).json({ error: 'Failed to add alert' });
  }
});

export default router;
