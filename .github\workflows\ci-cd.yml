name: Forex Trading Platform CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install Backend Dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Run Backend Linting
      working-directory: ./backend
      run: npm run lint
    
    - name: Run Backend Tests
      working-directory: ./backend
      env:
        NODE_ENV: test
        JWT_SECRET: test-secret-key
        ALPHA_VANTAGE_API_KEY: test-api-key
        REDIS_URL: redis://localhost:6379
      run: npm test -- --coverage --watchAll=false
    
    - name: Upload Backend Coverage
      uses: actions/upload-artifact@v3
      with:
        name: backend-coverage
        path: backend/coverage
    
    - name: Check Backend Test Coverage
      working-directory: ./backend
      run: |
        COVERAGE=$(cat coverage/coverage-summary.json | jq '.total.lines.pct')
        echo "Backend coverage: $COVERAGE%"
        if (( $(echo "$COVERAGE < 80" | bc -l) )); then
          echo "Coverage is below 80% threshold"
          exit 1
        fi

  mobile-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json
    
    - name: Install Mobile Dependencies
      working-directory: ./mobile
      run: npm ci
    
    - name: Run Mobile Tests
      working-directory: ./mobile
      run: npm test -- --coverage --watchAll=false
    
    - name: Upload Mobile Coverage
      uses: actions/upload-artifact@v3
      with:
        name: mobile-coverage
        path: mobile/coverage

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Security Audit
      run: |
        cd backend && npm audit --audit-level=high
        cd ../mobile && npm audit --audit-level=high
    
    - name: Run OWASP Dependency Check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'forex-trading-platform'
        path: '.'
        format: 'HTML'
    
    - name: Upload OWASP Report
      uses: actions/upload-artifact@v3
      with:
        name: owasp-report
        path: reports

  performance-test:
    runs-on: ubuntu-latest
    needs: [backend-tests]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
    
    - name: Install Dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Run Performance Tests
      working-directory: ./backend
      run: npm run test:performance
    
    - name: Upload Performance Results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: backend/performance-results

  deploy:
    runs-on: ubuntu-latest
    needs: [backend-tests, mobile-tests, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to Production
      run: |
        echo "Deploying to production..."
        # Add your deployment steps here
