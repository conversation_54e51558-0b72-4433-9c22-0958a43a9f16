import { performance } from 'perf_hooks';
import request from 'supertest';
import { app } from '../../src/index';
import WebSocket from 'ws';

describe('Performance Tests', () => {
  const validToken = 'test-jwt-token'; // Mock token for tests

  describe('API Response Time', () => {
    it('should respond to market data requests within 200ms (p95)', async () => {
      const responseTimes: number[] = [];
      const iterations = 100;

      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        await request(app)
          .get('/api/v1/market/EURUSD')
          .set('Authorization', `Bearer ${validToken}`);
        const end = performance.now();
        responseTimes.push(end - start);
      }

      // Calculate p95
      responseTimes.sort((a, b) => a - b);
      const p95Index = Math.floor(iterations * 0.95);
      const p95ResponseTime = responseTimes[p95Index];

      expect(p95ResponseTime).toBeLessThan(200);
    });

    it('should handle concurrent requests efficiently', async () => {
      const concurrentRequests = 50;
      const start = performance.now();

      const requests = Array(concurrentRequests).fill(null).map(() =>
        request(app)
          .get('/api/v1/market/EURUSD')
          .set('Authorization', `Bearer ${validToken}`)
      );

      await Promise.all(requests);
      const totalTime = performance.now() - start;

      // Should handle 50 concurrent requests in under 5 seconds
      expect(totalTime).toBeLessThan(5000);
    });
  });

  describe('WebSocket Performance', () => {
    let ws: WebSocket;

    beforeEach((done) => {
      ws = new WebSocket('ws://localhost:5000');
      ws.on('open', done);
    });

    afterEach(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    });

    it('should maintain low latency for price updates (<50ms)', (done) => {
      const latencies: number[] = [];
      let messageCount = 0;

      ws.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'priceUpdate') {
          const latency = Date.now() - message.timestamp;
          latencies.push(latency);
          messageCount++;

          if (messageCount >= 100) {
            const avgLatency = latencies.reduce((a, b) => a + b) / latencies.length;
            expect(avgLatency).toBeLessThan(50);
            done();
          }
        }
      });

      // Subscribe to get price updates
      ws.send(JSON.stringify({
        type: 'subscribe',
        pairs: ['EURUSD', 'GBPUSD', 'USDJPY']
      }));
    });
  });
});

