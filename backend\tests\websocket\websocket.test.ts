import { Server } from 'socket.io';
import Client from 'socket.io-client';
import http from 'http';
import { app } from '../../src/index';
import { marketDataService } from '../../src/services/marketData.service';

// Mock the market data service
jest.mock('../../../src/services/marketData.service');

const mockedMarketDataService = marketDataService as jest.Mocked<typeof marketDataService>;

describe('WebSocket Server Tests', () => {
  let server: http.Server;
  let io: Server;
  let clientSocket: any;
  let serverSocket: any;
  const port = 5001;

  beforeAll((done) => {
    // Create HTTP server
    server = http.createServer(app);
    
    // Initialize Socket.IO
    io = new Server(server, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
      },
    });

    // Handle connections
    io.on('connection', (socket) => {
      serverSocket = socket;
      
      // Handle subscribing to currency pairs
      socket.on('subscribe', (pairs: string[]) => {
        pairs.forEach(pair => {
          socket.join(`price-${pair}`);
        });
        socket.emit('subscribed', pairs);
      });

      // Handle unsubscribing
      socket.on('unsubscribe', (pairs: string[]) => {
        pairs.forEach(pair => {
          socket.leave(`price-${pair}`);
        });
        socket.emit('unsubscribed', pairs);
      });
    });

    server.listen(port, () => {
      done();
    });
  });

  afterAll((done) => {
    io.close();
    server.close();
    done();
  });

  beforeEach((done) => {
    // Connect client
    clientSocket = Client(`http://localhost:${port}`, {
      transports: ['websocket'],
    });
    
    clientSocket.on('connect', done);
  });

  afterEach(() => {
    if (clientSocket.connected) {
      clientSocket.disconnect();
    }
  });

  describe('Connection', () => {
    it('should connect successfully', (done) => {
      expect(clientSocket.connected).toBe(true);
      done();
    });

    it('should handle disconnection', (done) => {
      clientSocket.on('disconnect', () => {
        expect(clientSocket.connected).toBe(false);
        done();
      });
      
      clientSocket.disconnect();
    });
  });

  describe('Subscriptions', () => {
    it('should subscribe to currency pairs', (done) => {
      const pairs = ['EURUSD', 'GBPUSD'];
      
      clientSocket.on('subscribed', (subscribedPairs: string[]) => {
        expect(subscribedPairs).toEqual(pairs);
        done();
      });
      
      clientSocket.emit('subscribe', pairs);
    });

    it('should unsubscribe from currency pairs', (done) => {
      const pairs = ['EURUSD'];
      
      clientSocket.on('unsubscribed', (unsubscribedPairs: string[]) => {
        expect(unsubscribedPairs).toEqual(pairs);
        done();
      });
      
      clientSocket.emit('unsubscribe', pairs);
    });

    it('should handle multiple subscriptions', (done) => {
      let subscriptionCount = 0;
      const allPairs = ['EURUSD', 'GBPUSD', 'USDJPY'];
      
      clientSocket.on('subscribed', (pairs: string[]) => {
        subscriptionCount++;
        
        if (subscriptionCount === 2) {
          expect(true).toBe(true); // Both subscriptions successful
          done();
        }
      });
      
      clientSocket.emit('subscribe', ['EURUSD', 'GBPUSD']);
      setTimeout(() => {
        clientSocket.emit('subscribe', ['USDJPY']);
      }, 100);
    });
  });

  describe('Price Updates', () => {
    it('should receive price updates for subscribed pairs', (done) => {
      const mockPriceUpdate = {
        symbol: 'EURUSD',
        bid: 1.1000,
        ask: 1.1002,
        timestamp: new Date(),
        change: 0.0005,
        changePercent: 0.05,
      };

      clientSocket.on('subscribed', () => {
        // Simulate server sending price update
        io.to('price-EURUSD').emit('priceUpdate', mockPriceUpdate);
      });

      clientSocket.on('priceUpdate', (priceData: any) => {
        expect(priceData.symbol).toBe('EURUSD');
        expect(priceData.bid).toBe(1.1000);
        done();
      });

      clientSocket.emit('subscribe', ['EURUSD']);
    });

    it('should not receive updates for unsubscribed pairs', (done) => {
      let receivedUpdate = false;

      clientSocket.on('priceUpdate', () => {
        receivedUpdate = true;
      });

      clientSocket.on('unsubscribed', () => {
        // Send update after unsubscribe
        io.to('price-EURUSD').emit('priceUpdate', { symbol: 'EURUSD' });
        
        setTimeout(() => {
          expect(receivedUpdate).toBe(false);
          done();
        }, 200);
      });

      // Subscribe then unsubscribe
      clientSocket.emit('subscribe', ['EURUSD']);
      setTimeout(() => {
        clientSocket.emit('unsubscribe', ['EURUSD']);
      }, 100);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid subscription data', (done) => {
      clientSocket.on('error', (error: any) => {
        expect(error.message).toContain('Invalid subscription data');
        done();
      });

      // Send invalid data
      clientSocket.emit('subscribe', 'invalid-not-array');
    });

    it('should handle connection errors', (done) => {
      const errorClient = Client('http://localhost:9999', {
        transports: ['websocket'],
        reconnection: false,
      });

      errorClient.on('connect_error', (error: any) => {
        expect(error.type).toBe('TransportError');
        errorClient.close();
        done();
      });
    });
  });

  describe('Performance', () => {
    it('should handle multiple concurrent connections', (done) => {
      const clients: any[] = [];
      const connectionCount = 10;
      let connected = 0;

      for (let i = 0; i < connectionCount; i++) {
        const client = Client(`http://localhost:${port}`, {
          transports: ['websocket'],
        });

        client.on('connect', () => {
          connected++;
          if (connected === connectionCount) {
            // All clients connected
            expect(connected).toBe(connectionCount);
            
            // Cleanup
            clients.forEach(c => c.disconnect());
            done();
          }
        });

        clients.push(client);
      }
    });

    it('should handle rapid subscription changes', (done) => {
      const pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'NZDUSD'];
      let operations = 0;
      const totalOperations = 20;

      const performOperation = () => {
        if (operations >= totalOperations) {
          done();
          return;
        }

        const operation = operations % 2 === 0 ? 'subscribe' : 'unsubscribe';
        const selectedPairs = pairs.slice(0, Math.floor(Math.random() * pairs.length) + 1);
        
        clientSocket.emit(operation, selectedPairs);
        operations++;
        
        setTimeout(performOperation, 50);
      };

      performOperation();
    });
  });
});

