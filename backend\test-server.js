const express = require('express');
const cors = require('cors');
const http = require('http');
const socketIo = require('socket.io');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// Test endpoints
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'Forex Trading Platform API is running!',
    features: {
      realTimePrices: true,
      authentication: false,
      database: false,
      websocket: true
    }
  });
});

// Mock forex prices
const mockPrices = {
  EURUSD: { bid: 1.0950, ask: 1.0952, change: 0.0012 },
  GBPUSD: { bid: 1.2850, ask: 1.2852, change: -0.0023 },
  USDJPY: { bid: 145.50, ask: 145.52, change: 0.25 },
  AUDUSD: { bid: 0.6750, ask: 0.6752, change: 0.0008 }
};

app.get('/api/market-data/prices', (req, res) => {
  res.json(mockPrices);
});

// Mock news
app.get('/api/news/latest', (req, res) => {
  res.json([
    { id: 1, title: "Fed signals rate pause", content: "Federal Reserve hints at keeping rates steady", timestamp: new Date() },
    { id: 2, title: "EUR strengthens against USD", content: "Euro gains on positive economic data", timestamp: new Date() }
  ]);
});

// WebSocket for real-time prices
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  // Send initial prices
  socket.emit('prices', mockPrices);
  
  // Simulate price updates every 2 seconds
  const priceInterval = setInterval(() => {
    // Random price fluctuations
    Object.keys(mockPrices).forEach(pair => {
      const change = (Math.random() - 0.5) * 0.0010;
      mockPrices[pair].bid += change;
      mockPrices[pair].ask += change;
      mockPrices[pair].change += change;
    });
    
    socket.emit('prices', mockPrices);
  }, 2000);
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
    clearInterval(priceInterval);
  });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`\n🚀 Forex Trading Platform Test Server`);
  console.log(`====================================`);
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`✅ API: http://localhost:${PORT}/api/test`);
  console.log(`✅ Health: http://localhost:${PORT}/health`);
  console.log(`✅ Prices: http://localhost:${PORT}/api/market-data/prices`);
  console.log(`✅ News: http://localhost:${PORT}/api/news/latest`);
  console.log(`✅ WebSocket: ws://localhost:${PORT}`);
  console.log(`====================================\n`);
});
