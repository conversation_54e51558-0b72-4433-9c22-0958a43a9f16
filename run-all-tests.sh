#!/bin/bash
# Forex Trading Platform - Test Execution Script

echo "🚀 Starting Forex Trading Platform Test Suite"
echo "============================================"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to run tests with proper error handling
run_test() {
    local test_name=$1
    local test_command=$2
    local directory=$3
    
    echo -e "\n${YELLOW}Running $test_name...${NC}"
    cd "$directory" || exit 1
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ $test_name passed${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name failed${NC}"
        return 1
    fi
}

# Check if dependencies are installed
check_dependencies() {
    local dir=$1
    if [ ! -d "$dir/node_modules" ]; then
        echo -e "${RED}Dependencies not installed in $dir${NC}"
        echo "Running npm install..."
        cd "$dir" && npm install
    fi
}

# Start testing
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR" || exit 1

# Backend Tests
echo -e "\n${YELLOW}=== BACKEND TESTS ===${NC}"
check_dependencies "backend"

# Run different test suites
run_test "Backend Unit Tests" "npm test -- tests/unit" "backend"
run_test "Backend Security Tests" "npm test -- tests/security" "backend"
run_test "Backend WebSocket Tests" "npm test -- tests/websocket" "backend"
run_test "Backend Performance Tests" "npm test -- tests/performance" "backend"

# Generate backend coverage report
echo -e "\n${YELLOW}Generating Backend Coverage Report...${NC}"
cd backend && npm test -- --coverage

# Mobile Tests
echo -e "\n${YELLOW}=== MOBILE TESTS ===${NC}"
cd "$SCRIPT_DIR" || exit 1
check_dependencies "mobile"

run_test "Mobile Unit Tests" "npm test" "mobile"

# Summary
echo -e "\n${YELLOW}=== TEST SUMMARY ===${NC}"
echo "Test execution completed!"
echo "Check coverage reports in:"
echo "  - backend/coverage/index.html"
echo "  - mobile/coverage/index.html"

# Run security audit
echo -e "\n${YELLOW}=== SECURITY AUDIT ===${NC}"
cd "$SCRIPT_DIR/backend" && npm audit
cd "$SCRIPT_DIR/mobile" && npm audit

echo -e "\n${GREEN}✨ Test suite execution completed!${NC}"
