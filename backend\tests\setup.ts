import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.ALPHA_VANTAGE_API_KEY = 'test-alpha-vantage-key';
process.env.FINNHUB_API_KEY = 'test-finnhub-key';
process.env.FRED_API_KEY = 'test-fred-key';

let mongoServer: MongoMemoryServer;
let redisClient: any;

// Setup before all tests
beforeAll(async () => {
  // Setup MongoDB Memory Server
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  await mongoose.connect(mongoUri);

  // Mock Redis client
  redisClient = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    flushall: jest.fn(),
    quit: jest.fn(),
    on: jest.fn(),
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
  };

  // Mock Redis module
  jest.doMock('redis', () => ({
    createClient: jest.fn(() => redisClient),
  }));
});

// Cleanup after all tests
afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
  if (redisClient) {
    await redisClient.quit();
  }
});

// Clear database between tests
afterEach(async () => {
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
  jest.clearAllMocks();
});

// Mock Firebase Admin
jest.mock('../src/config/firebase', () => ({
  auth: () => ({
    verifyIdToken: jest.fn().mockResolvedValue({
      uid: 'test-uid',
      email: '<EMAIL>',
    }),
    createCustomToken: jest.fn().mockResolvedValue('test-token'),
  }),
}));

// Mock external APIs
jest.mock('axios');

// Mock Socket.io
jest.mock('socket.io', () => ({
  Server: jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    emit: jest.fn(),
    to: jest.fn().mockReturnThis(),
    use: jest.fn(),
  })),
}));

// Mock cron jobs
jest.mock('node-cron', () => ({
  schedule: jest.fn(),
}));

// Increase timeout for async operations
jest.setTimeout(30000);
