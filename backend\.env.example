# Server Configuration
NODE_ENV=development
PORT=5000
CLIENT_URL=http://localhost:3000

# Database
MONGODB_URI=mongodb+srv://your-username:<EMAIL>/forex-trading?retryWrites=true&w=majority

# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Redis (for caching)
REDIS_URL=redis://localhost:6379

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key

# API Keys
ALPHA_VANTAGE_API_KEY=AC8KQGGQTFAEVIDS
FINNHUB_API_KEY=d0sha6hr01qkkplv9k9gd0sha6hr01qkkplv9ka0
FRED_API_KEY=3129b1c6fe07abd074ecdc8cfa52247f

# Email Service (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Push Notifications (optional)
FCM_SERVER_KEY=your-fcm-server-key
