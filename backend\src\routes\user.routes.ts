 displayName;
    if (photoURL) req.dbUser.photoURL = photoURL;
    
    await req.dbUser.save();
    
    res.json({ user: req.dbUser });
  } catch (error) {
    logger.error('Update profile error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Get user statistics
router.get('/stats', verifyToken, async (req: any, res) => {
  try {
    const stats = {
      totalWatchlists: 0, // Will be populated from Watchlist model
      totalAlerts: 0,
      favoritesPairs: req.dbUser.preferences.favoritesPairs.length,
      memberSince: req.dbUser.createdAt,
      lastLogin: req.dbUser.lastLogin,
    };
    
    res.json({ stats });
  } catch (error) {
    logger.error('Get stats error:', error);
    res.status(500).json({ error: 'Failed to get statistics' });
  }
});

export default router;
