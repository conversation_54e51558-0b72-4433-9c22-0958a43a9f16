import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { auth } from '../config/firebase';

export interface AuthRequest extends Request {
  user?: {
    id: string;
    email: string;
    provider?: string;
  };
  rateLimitKey?: string;
}

export const authMiddleware = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({ error: 'No authorization header provided' });
    }

    if (!authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Invalid authorization format' });
    }

    const token = authHeader.substring(7);
    const authProvider = req.headers['x-auth-provider'];

    if (authProvider === 'firebase') {
      // Firebase authentication
      try {
        const decodedToken = await auth().verifyIdToken(token);
        req.user = {
          id: decodedToken.uid,
          email: decodedToken.email || '',
          provider: 'firebase'
        };
      } catch (error) {
        return res.status(401).json({ error: 'Firebase authentication failed' });
      }
    } else {
      // JWT authentication
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
        req.user = {
          id: decoded.id,
          email: decoded.email
        };
      } catch (error) {
        if (error instanceof jwt.TokenExpiredError) {
          return res.status(401).json({ error: 'Token expired' });
        }
        if (error instanceof jwt.JsonWebTokenError) {
          return res.status(401).json({ error: 'Invalid token' });
        }
        throw error;
      }
    }

    // Set rate limit key for authenticated users
    req.rateLimitKey = `user:${req.user.id}`;
    
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({ error: 'Authentication error' });
  }
};
