import { io, Socket } from 'socket.io-client';
import { store } from '../store';
import { updateSinglePrice } from '../store/slices/marketDataSlice';
import { API_BASE_URL } from '../config/constants';

let socket: Socket | null = null;

export const connectWebSocket = () => {
  if (socket?.connected) return;

  socket = io(API_BASE_URL, {
    transports: ['websocket'],
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
  });

  socket.on('connect', () => {
    console.log('WebSocket connected');
    
    // Subscribe to watched pairs
    const state = store.getState();
    const watchedPairs = state.marketData.watchedPairs;
    socket?.emit('subscribe', watchedPairs);
  });

  socket.on('priceUpdate', (data) => {
    store.dispatch(updateSinglePrice(data));
  });

  socket.on('disconnect', () => {
    console.log('WebSocket disconnected');
  });

  socket.on('error', (error) => {
    console.error('WebSocket error:', error);
  });
};

export const subscribeToPair = (pair: string) => {
  if (socket?.connected) {
    socket.emit('subscribe', [pair]);
  }
};

export const unsubscribeFromPair = (pair: string) => {
  if (socket?.connected) {
    socket.emit('unsubscribe', [pair]);
  }
};

export const disconnectWebSocket = () => {
  if (socket?.connected) {
    socket.disconnect();
    socket = null;
  }
};
