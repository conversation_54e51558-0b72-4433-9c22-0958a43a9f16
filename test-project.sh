#!/bin/bash
echo "======================================"
echo "Forex Trading Platform Test Report"
echo "======================================"
echo ""

# Check Node.js and npm
echo "1. Environment Check:"
echo "--------------------"
node --version
npm --version
echo ""

# Check backend
echo "2. Backend Analysis:"
echo "--------------------"
echo "Checking backend dependencies..."
cd backend
if [ -d "node_modules" ]; then
    echo "✓ Backend dependencies installed"
else
    echo "✗ Backend dependencies not installed"
fi

echo ""
echo "Backend directory structure:"
find src -type f -name "*.ts" | head -20
echo ""

# Check mobile
echo "3. Mobile App Analysis:"
echo "--------------------"
cd ../mobile
if [ -d "node_modules" ]; then
    echo "✓ Mobile dependencies installed"
else
    echo "✗ Mobile dependencies not installed"
fi

echo ""
echo "Mobile directory structure:"
find src -type f -name "*.tsx" -o -name "*.ts" | head -20
echo ""

# Check for test files
echo "4. Test Coverage Analysis:"
echo "--------------------"
cd ..
echo "Backend tests:"
find backend/tests -type f -name "*.test.ts" -o -name "*.spec.ts" 2>/dev/null | wc -l
echo "Mobile tests:"
find mobile/__tests__ -type f -name "*.test.tsx" -o -name "*.test.ts" 2>/dev/null | wc -l
echo ""

# Check for critical files
echo "5. Critical Files Check:"
echo "--------------------"
[ -f "backend/.env" ] && echo "✓ Backend .env exists" || echo "✗ Backend .env missing"
[ -f "backend/src/index.ts" ] && echo "✓ Backend entry point exists" || echo "✗ Backend entry point missing"
[ -f "mobile/App.tsx" ] && echo "✓ Mobile entry point exists" || echo "✗ Mobile entry point missing"
[ -f "mobile/src/screens/DashboardScreen.tsx" ] && echo "✓ Dashboard screen exists" || echo "✗ Dashboard screen missing"
echo ""

echo "======================================"
echo "Test Complete"
echo "======================================"
