#!/bin/bash

# Forex Trading Platform - Test Runner Script

echo "🧪 Forex Trading Platform - Comprehensive Test Suite"
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to run backend tests
run_backend_tests() {
    echo -e "\n${YELLOW}Running Backend Tests...${NC}"
    cd backend
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        npm install
    fi
    
    # Run unit tests
    echo -e "${GREEN}Running unit tests...${NC}"
    npm test -- --testPathPattern=unit
    
    # Run integration tests
    echo -e "${GREEN}Running integration tests...${NC}"
    npm test -- --testPathPattern=integration
    
    # Generate coverage report
    echo -e "${GREEN}Generating coverage report...${NC}"
    npm test -- --coverage
    
    cd ..
}

# Function to run mobile tests
run_mobile_tests() {
    echo -e "\n${YELLOW}Running Mobile Tests...${NC}"
    cd mobile
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        npm install
    fi
    
    # Run component tests
    echo -e "${GREEN}Running component tests...${NC}"
    npm test
    
    cd ..
}

# Function to run security tests
run_security_tests() {
    echo -e "\n${YELLOW}Running Security Tests...${NC}"
    
    # Check for exposed API keys
    echo -e "${GREEN}Checking for exposed API keys...${NC}"
    grep -r "ALPHA_VANTAGE_API_KEY" --exclude-dir=node_modules --exclude="*.md" .
    
    # Check for hardcoded credentials
    echo -e "${GREEN}Checking for hardcoded credentials...${NC}"
    grep -r "password\|secret\|key" --exclude-dir=node_modules --exclude="*.md" . | grep -v "example\|test"
}

# Function to run performance tests
run_performance_tests() {
    echo -e "\n${YELLOW}Running Performance Tests...${NC}"
    
    # Run load tests if k6 is installed
    if command -v k6 &> /dev/null; then
        echo -e "${GREEN}Running load tests with k6...${NC}"
        # k6 run backend/tests/performance/load-test.js
    else
        echo -e "${RED}k6 not installed. Skipping load tests.${NC}"
    fi
}

# Main execution
echo "Select test suite to run:"
echo "1) All tests"
echo "2) Backend tests only"
echo "3) Mobile tests only"
echo "4) Security tests only"
echo "5) Performance tests only"

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        run_backend_tests
        run_mobile_tests
        run_security_tests
        run_performance_tests
        ;;
    2)
        run_backend_tests
        ;;
    3)
        run_mobile_tests
        ;;
    4)
        run_security_tests
        ;;
    5)
        run_performance_tests
        ;;
    *)
        echo -e "${RED}Invalid choice. Exiting.${NC}"
        exit 1
        ;;
esac

echo -e "\n${GREEN}✅ Test suite completed!${NC}"
