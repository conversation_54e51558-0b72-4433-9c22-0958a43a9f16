import { Router } from 'express';
import { marketDataService } from '../services/marketData.service';
import { apiRateLimiter } from '../middleware/rateLimiter';

const router = Router();

// Get current price for a forex pair
router.get('/price/:pair', apiRateLimiter, async (req, res) => {
  try {
    const { pair } = req.params;
    const data = await marketDataService.fetchForexData(pair.toUpperCase());
    
    if (!data) {
      return res.status(404).json({ error: 'Pair not found' });
    }
    
    return res.json({ data });
  } catch (error) {
    return res.status(500).json({ error: 'Failed to fetch price data' });
  }
});

// Get prices for multiple pairs
router.post('/prices', apiRateLimiter, async (req, res) => {
  try {
    const { pairs } = req.body;
    if (!Array.isArray(pairs)) {
      return res.status(400).json({ error: 'Pairs must be an array' });
    }
    
    const data = await Promise.all(pairs.map((pair: string) =>
      marketDataService.fetchForexData(pair.toUpperCase())
    ));
    return res.json({ data });
  } catch (error) {
    return res.status(500).json({ error: 'Failed to fetch price data' });
  }
});

// Get historical data
router.get('/history/:pair', apiRateLimiter, async (req, res) => {
  try {
    const { pair } = req.params;
    const { interval = 'daily' } = req.query;
    
    const data = await marketDataService.fetchHistoricalData(
      pair.toUpperCase(),
      interval as string
    );

    return res.json({ data });
  } catch (error) {
    return res.status(500).json({ error: 'Failed to fetch historical data' });
  }
});

export default router;
