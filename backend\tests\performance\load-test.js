import http from 'k6/http';
import { check, sleep } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';

// Custom metrics
const apiErrors = new Counter('api_errors');
const apiSuccessRate = new Rate('api_success_rate');
const apiResponseTime = new Trend('api_response_time');

// Test configuration
export const options = {
  stages: [
    { duration: '30s', target: 10 },   // Ramp up to 10 users
    { duration: '1m', target: 50 },    // Ramp up to 50 users
    { duration: '2m', target: 100 },   // Stay at 100 users
    { duration: '1m', target: 50 },    // Ramp down to 50 users
    { duration: '30s', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    http_req_failed: ['rate<0.1'],    // Error rate must be below 10%
    api_success_rate: ['rate>0.9'],   // API success rate must be above 90%
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:5000';
const AUTH_TOKEN = __ENV.AUTH_TOKEN || 'test-token';

// Helper function to make authenticated requests
function makeAuthRequest(url, payload, params = {}) {
  const defaultParams = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  };
  
  return http.post(url, JSON.stringify(payload), { ...defaultParams, ...params });
}

export default function () {
  // Test 1: Fetch multiple currency prices
  const pricesPayload = {
    pairs: ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'NZDUSD'],
  };
  
  const pricesResponse = makeAuthRequest(
    `${BASE_URL}/api/market-data/prices`,
    pricesPayload
  );
  
  const pricesSuccess = check(pricesResponse, {
    'prices status is 200': (r) => r.status === 200,
    'prices response has data': (r) => {
      const body = JSON.parse(r.body);
      return body.data && Object.keys(body.data).length > 0;
    },
    'prices response time < 500ms': (r) => r.timings.duration < 500,
  });
  
  apiSuccessRate.add(pricesSuccess);
  apiResponseTime.add(pricesResponse.timings.duration);
  if (!pricesSuccess) apiErrors.add(1);
  
  sleep(1); // Wait 1 second between requests
  
  // Test 2: Fetch historical data
  const pairs = ['EURUSD', 'GBPUSD', 'USDJPY'];
  const randomPair = pairs[Math.floor(Math.random() * pairs.length)];
  
  const historyResponse = http.get(
    `${BASE_URL}/api/market-data/history/${randomPair}?interval=1h&outputsize=compact`,
    {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      },
    }
  );
  
  const historySuccess = check(historyResponse, {
    'history status is 200': (r) => r.status === 200,
    'history response has data': (r) => {
      const body = JSON.parse(r.body);
      return body.data && Array.isArray(body.data);
    },
    'history response time < 1000ms': (r) => r.timings.duration < 1000,
  });
  
  apiSuccessRate.add(historySuccess);
  apiResponseTime.add(historyResponse.timings.duration);
  if (!historySuccess) apiErrors.add(1);
  
  sleep(0.5);
  
  // Test 3: Fetch news
  const newsResponse = http.get(
    `${BASE_URL}/api/news/latest?limit=10`,
    {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      },
    }
  );
  
  check(newsResponse, {
    'news status is 200': (r) => r.status === 200,
    'news response has articles': (r) => {
      const body = JSON.parse(r.body);
      return body.news && Array.isArray(body.news);
    },
  });
  
  sleep(0.5);
  
  // Test 4: Technical indicators
  const indicatorsPayload = {
    pair: randomPair,
    indicators: ['sma', 'rsi', 'macd'],
    period: 14,
  };
  
  const indicatorsResponse = makeAuthRequest(
    `${BASE_URL}/api/analysis/indicators`,
    indicatorsPayload
  );
  
  check(indicatorsResponse, {
    'indicators status is 200': (r) => r.status === 200,
    'indicators response has results': (r) => {
      const body = JSON.parse(r.body);
      return body.results && Array.isArray(body.results);
    },
  });
  
  sleep(1);
}

// Lifecycle hooks
export function setup() {
  // Perform authentication and get token
  console.log('Setting up load test...');
  
  // Verify the API is accessible
  const healthCheck = http.get(`${BASE_URL}/health`);
  check(healthCheck, {
    'API is healthy': (r) => r.status === 200,
  });
  
  return { startTime: new Date() };
}

export function teardown(data) {
  console.log(`Load test completed. Started at: ${data.startTime}`);
}

// Custom summary report
export function handleSummary(data) {
  return {
    'summary.html': htmlReport(data),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}

function htmlReport(data) {
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Load Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .metric { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { color: green; }
        .failure { color: red; }
    </style>
</head>
<body>
    <h1>Forex Trading Platform - Load Test Results</h1>
    <div class="metric">
        <h3>Request Duration (95th percentile)</h3>
        <p>${data.metrics.http_req_duration.values['p(95)']}ms</p>
    </div>
    <div class="metric">
        <h3>Success Rate</h3>
        <p class="${data.metrics.api_success_rate.values.rate > 0.9 ? 'success' : 'failure'}">
            ${(data.metrics.api_success_rate.values.rate * 100).toFixed(2)}%
        </p>
    </div>
    <div class="metric">
        <h3>Total Requests</h3>
        <p>${data.metrics.http_reqs.values.count}</p>
    </div>
    <div class="metric">
        <h3>Failed Requests</h3>
        <p>${data.metrics.http_req_failed.values.count}</p>
    </div>
</body>
</html>
  `;
}
