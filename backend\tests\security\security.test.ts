import request from 'supertest';
import { app } from '../../src/index';

describe('Security Tests', () => {
  describe('Input Validation', () => {
    it('should prevent SQL injection attempts', async () => {
      const sqlInjectionPayloads = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "admin'--",
        "1; UPDATE users SET role='admin' WHERE id=1;",
      ];

      for (const payload of sqlInjectionPayloads) {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: payload,
            displayName: 'Test',
            firebaseUid: 'test-uid',
          })
          .expect(400);

        expect(response.body.error).toBeDefined();
      }
    });

    it('should prevent NoSQL injection attempts', async () => {
      const noSqlInjectionPayloads = [
        { $ne: null },
        { $gt: '' },
        { $regex: '.*' },
        { $where: 'this.password == this.password' },
      ];

      for (const payload of noSqlInjectionPayloads) {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: payload,
            displayName: 'Test',
            firebaseUid: 'test-uid',
          })
          .expect(400);

        expect(response.body.error).toBeDefined();
      }
    });

    it('should prevent XSS attacks in user input', async () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        '<img src=x onerror=alert("XSS")>',
        'javascript:alert("XSS")',
        '<svg onload=alert("XSS")>',
      ];

      for (const payload of xssPayloads) {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: '<EMAIL>',
            displayName: payload,
            firebaseUid: 'test-uid',
          });

        // Check that response doesn't contain unescaped payload
        const responseText = JSON.stringify(response.body);
        expect(responseText).not.toContain(payload);
        expect(responseText).not.toContain('<script>');
      }
    });

    it('should validate email format', async () => {
      const invalidEmails = [
        'notanemail',
        '@example.com',
        'user@',
        'user@.com',
        'user@domain',
        'user <EMAIL>',
      ];

      for (const email of invalidEmails) {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email,
            displayName: 'Test User',
            firebaseUid: 'test-uid',
          })
          .expect(400);

        expect(response.body.error).toContain('email');
      }
    });
  });

  describe('Authentication & Authorization', () => {
    it('should reject requests without authentication token', async () => {
      const protectedEndpoints = [
        '/api/market-data/prices',
        '/api/analysis/indicators',
        '/api/watchlist',
        '/api/user/preferences',
      ];

      for (const endpoint of protectedEndpoints) {
        const response = await request(app)
          .get(endpoint)
          .expect(401);

        expect(response.body.error).toBe('No token provided');
      }
    });

    it('should reject requests with invalid tokens', async () => {
      const response = await request(app)
        .get('/api/market-data/prices')
        .set('Authorization', 'Bearer invalid-token-12345')
        .expect(401);

      expect(response.body.error).toBe('Invalid token');
    });

    it('should reject expired tokens', async () => {
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MDAwMDAwMDB9.expired';
      
      const response = await request(app)
        .get('/api/market-data/prices')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body.error).toBe('Invalid token');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits on authentication endpoints', async () => {
      const requests = [];
      
      // Make 10 rapid requests
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(app)
            .post('/api/auth/register')
            .send({
              email: `test${i}@example.com`,
              displayName: 'Test',
              firebaseUid: `uid-${i}`,
            })
        );
      }

      const responses = await Promise.all(requests);
      const rateLimited = responses.some(res => res.status === 429);
      
      expect(rateLimited).toBe(true);
    });

    it('should have different rate limits for different endpoints', async () => {
      // API endpoints should allow more requests than auth endpoints
      const apiRequests = [];
      
      for (let i = 0; i < 100; i++) {
        apiRequests.push(
          request(app)
            .get('/api/market-data/supported-pairs')
            .set('Authorization', 'Bearer valid-token')
        );
      }

      const responses = await Promise.all(apiRequests);
      const successfulRequests = responses.filter(res => res.status === 200);
      
      // Should allow more API requests than auth requests
      expect(successfulRequests.length).toBeGreaterThan(10);
    });
  });

  describe('Headers Security', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      // Check for security headers
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['strict-transport-security']).toBeDefined();
    });

    it('should not expose sensitive information in headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      // Should not expose server details
      expect(response.headers['x-powered-by']).toBeUndefined();
      expect(response.headers['server']).not.toContain('Express');
    });
  });

  describe('CORS Configuration', () => {
    it('should properly configure CORS', async () => {
      const response = await request(app)
        .options('/api/market-data/prices')
        .set('Origin', 'https://example.com')
        .expect(204);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
      expect(response.headers['access-control-allow-methods']).toContain('GET');
      expect(response.headers['access-control-allow-methods']).toContain('POST');
    });

    it('should not allow unauthorized origins', async () => {
      const response = await request(app)
        .get('/api/market-data/prices')
        .set('Origin', 'https://malicious-site.com')
        .expect(401); // Will fail auth, but CORS should be checked first

      // Verify CORS headers based on configuration
    });
  });

  describe('Data Validation', () => {
    it('should validate currency pair format', async () => {
      const invalidPairs = [
        'EUR',
        'EURUSDGBP',
        '123456',
        'EUR/USD',
        'eur-usd',
        '<script>alert("XSS")</script>',
      ];

      for (const pair of invalidPairs) {
        const response = await request(app)
          .get(`/api/market-data/history/${pair}`)
          .set('Authorization', 'Bearer valid-token')
          .expect(400);

        expect(response.body.error).toContain('Invalid currency pair');
      }
    });

    it('should validate numeric parameters', async () => {
      const response = await request(app)
        .post('/api/analysis/indicators')
        .set('Authorization', 'Bearer valid-token')
        .send({
          pair: 'EURUSD',
          indicators: ['sma'],
          period: 'not-a-number',
        })
        .expect(400);

      expect(response.body.error).toContain('period');
    });
  });

  describe('API Key Security', () => {
    it('should not expose API keys in responses', async () => {
      const response = await request(app)
        .get('/api/config')
        .set('Authorization', 'Bearer valid-token');

      const responseText = JSON.stringify(response.body);
      
      // Check for common API key patterns
      expect(responseText).not.toMatch(/api[_-]?key/i);
      expect(responseText).not.toMatch(/secret/i);
      expect(responseText).not.toMatch(/password/i);
      expect(responseText).not.toMatch(/token/i);
    });
  });
});

