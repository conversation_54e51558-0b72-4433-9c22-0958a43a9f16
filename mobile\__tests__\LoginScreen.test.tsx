import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { configureStore } from '@reduxjs/toolkit';
import LoginScreen from '../src/screens/LoginScreen';
import { authSlice } from '../src/store/slices/authSlice';
import { authService } from '../src/services/auth.service';

// Mock dependencies
jest.mock('../src/services/auth.service');
const mockAuthService = authService as jest.Mocked<typeof authService>;

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

// Create test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authSlice.reducer,
    },
  });
};

describe('LoginScreen', () => {
  let store;

  beforeEach(() => {
    jest.clearAllMocks();
    store = createTestStore();
  });

  const renderScreen = () => {
    return render(
      <Provider store={store}>
        <NavigationContainer>
          <LoginScreen />
        </NavigationContainer>
      </Provider>
    );
  };

  it('should render login form correctly', () => {
    const { getByPlaceholderText, getByText } = renderScreen();

    expect(getByPlaceholderText('Email')).toBeTruthy();
    expect(getByPlaceholderText('Password')).toBeTruthy();
    expect(getByText('Login')).toBeTruthy();
    expect(getByText("Don't have an account? Register")).toBeTruthy();
  });

  it('should handle successful login', async () => {
    mockAuthService.login.mockResolvedValue({
      user: { id: '123', email: '<EMAIL>' },
      token: 'mock-token',
    });

    const { getByPlaceholderText, getByText } = renderScreen();

    // Fill in form
    fireEvent.changeText(getByPlaceholderText('Email'), '<EMAIL>');
    fireEvent.changeText(getByPlaceholderText('Password'), 'password123');

    // Submit form
    fireEvent.press(getByText('Login'));

    await waitFor(() => {
      expect(mockAuthService.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(mockNavigate).toHaveBeenCalledWith('Dashboard');
    });
  });

  it('should display error on failed login', async () => {
    mockAuthService.login.mockRejectedValue(new Error('Invalid credentials'));

    const { getByPlaceholderText, getByText } = renderScreen();

    fireEvent.changeText(getByPlaceholderText('Email'), '<EMAIL>');
    fireEvent.changeText(getByPlaceholderText('Password'), 'wrongpassword');
    fireEvent.press(getByText('Login'));

    await waitFor(() => {
      expect(getByText('Invalid credentials')).toBeTruthy();
    });
  });

  it('should validate email format', async () => {
    const { getByPlaceholderText, getByText } = renderScreen();

    fireEvent.changeText(getByPlaceholderText('Email'), 'invalid-email');
    fireEvent.changeText(getByPlaceholderText('Password'), 'password123');
    fireEvent.press(getByText('Login'));

    await waitFor(() => {
      expect(getByText('Please enter a valid email')).toBeTruthy();
      expect(mockAuthService.login).not.toHaveBeenCalled();
    });
  });

  it('should navigate to register screen', () => {
    const { getByText } = renderScreen();

    fireEvent.press(getByText("Don't have an account? Register"));

    expect(mockNavigate).toHaveBeenCalledWith('Register');
  });

  it('should show loading state during login', async () => {
    mockAuthService.login.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));

    const { getByPlaceholderText, getByText, getByTestId } = renderScreen();

    fireEvent.changeText(getByPlaceholderText('Email'), '<EMAIL>');
    fireEvent.changeText(getByPlaceholderText('Password'), 'password123');
    fireEvent.press(getByText('Login'));

    await waitFor(() => {
      expect(getByTestId('loading-indicator')).toBeTruthy();
    });
  });
});
