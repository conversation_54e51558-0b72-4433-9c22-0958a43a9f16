import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

const PlaceholderScreen = ({ title }: { title: string }) => (
  <View style={styles.container}>
    <Text variant="headlineMedium">{title}</Text>
    <Text variant="bodyLarge" style={styles.text}>
      Coming soon!
    </Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    marginTop: 10,
    opacity: 0.7,
  },
});

export const MarketScreen = () => <PlaceholderScreen title="Market" />;
export const NewsScreen = () => <PlaceholderScreen title="News" />;
export const AnalysisScreen = () => <PlaceholderScreen title="Analysis" />;
export const ProfileScreen = () => <PlaceholderScreen title="Profile" />;
export const PairDetailScreen = () => <PlaceholderScreen title="Pair Details" />;
export const WatchlistScreen = () => <PlaceholderScreen title="Watchlist" />;
export const SettingsScreen = () => <PlaceholderScreen title="Settings" />;
