import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { Server } from 'socket.io';

// Load environment variables
dotenv.config();

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: process.env.CLIENT_URL || 'http://localhost:3000',
    credentials: true
  }
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(compression());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.status(200).json({ 
    message: 'Forex Trading Platform API is running!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Mock market data endpoint
app.get('/api/market-data/prices', (req, res) => {
  const mockPrices = {
    EURUSD: { bid: 1.0950, ask: 1.0952, timestamp: new Date().toISOString() },
    GBPUSD: { bid: 1.2850, ask: 1.2852, timestamp: new Date().toISOString() },
    USDJPY: { bid: 145.50, ask: 145.52, timestamp: new Date().toISOString() },
    AUDUSD: { bid: 0.6750, ask: 0.6752, timestamp: new Date().toISOString() }
  };
  res.status(200).json(mockPrices);
});

// WebSocket configuration
io.on('connection', (socket) => {
  console.log(`Client connected: ${socket.id}`);

  socket.on('subscribe', (pairs: string[]) => {
    console.log(`Client ${socket.id} subscribing to:`, pairs);
    pairs.forEach(pair => {
      socket.join(`price-${pair}`);
    });
  });

  socket.on('unsubscribe', (pairs: string[]) => {
    pairs.forEach(pair => {
      socket.leave(`price-${pair}`);
    });
  });

  socket.on('disconnect', () => {
    console.log(`Client disconnected: ${socket.id}`);
  });
});

// Start server
const PORT = process.env.PORT || 5000;

httpServer.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`API available at: http://localhost:${PORT}/api/test`);
  console.log(`Health check at: http://localhost:${PORT}/health`);
  console.log(`Mock prices at: http://localhost:${PORT}/api/market-data/prices`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  httpServer.close(() => {
    process.exit(0);
  });
});

export { app, io };
