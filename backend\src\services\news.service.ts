import axios from 'axios';
import cron from 'node-cron';
import { logger } from '../utils/logger';
import { cacheService } from './cache.service';
import NewsModel, { INews } from '../models/News';

class NewsService {
  async fetchEconomicCalendar() {
    try {
      const cached = await cacheService.get('economic-calendar');
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(
        `https://finnhub.io/api/v1/calendar/economic?token=${process.env.FINNHUB_API_KEY}`
      );

      const events = response.data.economicCalendar || [];
      
      // Cache for 1 hour
      await cacheService.set('economic-calendar', JSON.stringify(events), 3600);
      
      return events;
    } catch (error) {
      logger.error('Error fetching economic calendar:', error);
      return [];
    }
  }

  async fetchForexNews() {
    try {
      const cached = await cacheService.get('forex-news');
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(
        `https://finnhub.io/api/v1/news?category=forex&token=${process.env.FINNHUB_API_KEY}`
      );

      const news = response.data || [];
      
      // Save to database
      for (const article of news) {
        await this.saveNews({
          title: article.headline,
          summary: article.summary,
          url: article.url,
          source: article.source,
          category: 'forex',
          publishedAt: new Date(article.datetime * 1000),
          image: article.image,
        });
      }
      
      // Cache for 30 minutes
      await cacheService.set('forex-news', JSON.stringify(news), 1800);
      
      return news;
    } catch (error) {
      logger.error('Error fetching forex news:', error);
      return [];
    }
  }
  async saveNews(newsData: Partial<INews>) {
    try {
      // Check if news already exists
      const existing = await NewsModel.findOne({ url: newsData.url });
      if (!existing) {
        await NewsModel.create(newsData);
      }
    } catch (error) {
      logger.error('Error saving news:', error);
    }
  }

  async getNews(filters: any = {}, page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;
      
      const query: any = {};
      if (filters.category) query.category = filters.category;
      if (filters.search) {
        query.$or = [
          { title: { $regex: filters.search, $options: 'i' } },
          { summary: { $regex: filters.search, $options: 'i' } },
        ];
      }
      
      const [news, total] = await Promise.all([
        NewsModel.find(query)
          .sort({ publishedAt: -1 })
          .skip(skip)
          .limit(limit),
        NewsModel.countDocuments(query),
      ]);
      
      return {
        news,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting news:', error);
      return { news: [], pagination: { page: 1, limit, total: 0, pages: 0 } };
    }
  }

  async getLatestNews(limit: number = 20): Promise<any[]> {
    try {
      const news = await NewsModel.find({})
        .sort({ publishedAt: -1 })
        .limit(limit);
      return news;
    } catch (error) {
      logger.error('Error getting latest news:', error);
      return [];
    }
  }
}

export { NewsService };
export const newsService = new NewsService();

export const startNewsJobs = () => {
  // Fetch news every 30 minutes
  cron.schedule('*/30 * * * *', async () => {
    await newsService.fetchForexNews();
  });
  
  // Fetch economic calendar every hour
  cron.schedule('0 * * * *', async () => {
    await newsService.fetchEconomicCalendar();
  });
  
  logger.info('News jobs started');
};
