import mongoose, { Schema, Document } from 'mongoose';

export interface INews extends Document {
  id: string;
  headline: string;
  summary: string;
  url: string;
  image?: string;
  category: string;
  source: string;
  datetime: Date;
  related?: string[];
  createdAt: Date;
  updatedAt: Date;
}

const NewsSchema = new Schema<INews>(
  {
    id: { type: String, required: true, unique: true },
    headline: { type: String, required: true },
    summary: { type: String, required: true },
    url: { type: String, required: true },
    image: { type: String },
    category: { type: String, required: true, index: true },
    source: { type: String, required: true },
    datetime: { type: Date, required: true, index: true },
    related: [{ type: String }],
  },
  {
    timestamps: true,
  }
);

// Indexes for efficient queries
NewsSchema.index({ datetime: -1 });
NewsSchema.index({ category: 1, datetime: -1 });
NewsSchema.index({ headline: 'text', summary: 'text' });

export const News = mongoose.model<INews>('News', NewsSchema);
