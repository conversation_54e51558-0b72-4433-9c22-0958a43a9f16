{"extends": "expo/tsconfig.base", "compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "jsx": "react-native", "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "allowJs": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "types": ["react", "react-native", "jest"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@types/*": ["src/types/*"], "@store/*": ["src/store/*"], "@assets/*": ["assets/*"]}}, "include": ["src/**/*", "App.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}