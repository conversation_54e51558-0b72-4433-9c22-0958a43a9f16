import mongoose, { Document, Schema } from 'mongoose';

export interface IWatchlist extends Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  pairs: string[];
  alerts: {
    pair: string;
    type: 'price_above' | 'price_below' | 'percentage_change';
    value: number;
    enabled: boolean;
    triggeredAt?: Date;
  }[];
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const WatchlistSchema = new Schema<IWatchlist>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  name: {
    type: String,
    required: true,
  },
  description: String,
  pairs: [{
    type: String,
    uppercase: true,
  }],
  alerts: [{
    pair: {
      type: String,
      uppercase: true,
      required: true,
    },
    type: {
      type: String,
      enum: ['price_above', 'price_below', 'percentage_change'],
      required: true,
    },
    value: {
      type: Number,
      required: true,
    },
    enabled: {
      type: Boolean,
      default: true,
    },
    triggeredAt: Date,
  }],
  isDefault: {
    type: Boolean,
    default: false,
  },
}, {
  timestamps: true,
});

// Ensure only one default watchlist per user
WatchlistSchema.index({ userId: 1, isDefault: 1 }, { unique: true, partialFilterExpression: { isDefault: true } });

const Watchlist = mongoose.model<IWatchlist>('Watchlist', WatchlistSchema);
export { Watchlist };
export default Watchlist;
