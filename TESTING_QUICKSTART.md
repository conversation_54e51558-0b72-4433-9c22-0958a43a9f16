# Forex Trading Platform - Testing Quick Reference

## 🚀 Quick Start

```bash
# Fix dependencies first
cd backend && npm install
cd ../mobile && npm install

# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test suites
npm run test:unit
npm run test:security
npm run test:performance
```

## 📁 Test Structure

```
backend/tests/
├── unit/           # Unit tests for services
├── integration/    # API endpoint tests
├── security/       # Security & auth tests
├── performance/    # Performance benchmarks
└── websocket/      # Real-time connection tests

mobile/__tests__/
├── screens/        # Screen component tests
├── components/     # UI component tests
└── integration/    # App flow tests
```

## ✅ Test Checklist

### Backend
- [ ] Fix npm dependencies
- [ ] Run unit tests
- [ ] Run security tests
- [ ] Run performance tests
- [ ] Check coverage (>80%)

### Mobile
- [ ] Fix npm dependencies  
- [ ] Run component tests
- [ ] Test real-time updates
- [ ] Check coverage

### CI/CD
- [ ] Push to GitHub
- [ ] Monitor Actions workflow
- [ ] Review coverage reports

## 🔧 Troubleshooting

1. **Module not found errors**: Run `npm install`
2. **Test failures**: Check `.env` file exists
3. **Coverage issues**: Run `npm run test:coverage`
4. **CI failures**: Check GitHub Actions logs

## 📊 Coverage Goals

- Critical paths: 90%+
- Overall: 80%+
- Security: 100%
- API endpoints: 85%+
