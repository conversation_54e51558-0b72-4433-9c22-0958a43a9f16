import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import WebSocket from 'ws';
import PriceUpdateComponent from '../../src/components/PriceUpdateComponent';

// Mock WebSocket
jest.mock('ws');

const mockStore = configureStore([]);

describe('Real-time Price Updates', () => {
  let store: any;
  let mockWebSocket: any;

  beforeEach(() => {
    store = mockStore({
      market: {
        prices: {
          EURUSD: { bid: 1.1000, ask: 1.1002, change: 0.0005 },
          GBPUSD: { bid: 1.2500, ask: 1.2502, change: -0.0003 }
        },
        subscriptions: ['EURUSD', 'GBPUSD']
      },
      user: {
        authenticated: true
      }
    });

    mockWebSocket = {
      on: jest.fn(),
      send: jest.fn(),
      close: jest.fn(),
      readyState: WebSocket.OPEN
    };

    (WebSocket as jest.MockedClass<typeof WebSocket>).mockImplementation(() => mockWebSocket);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should display initial price data', () => {
    const { getByText } = render(
      <Provider store={store}>
        <PriceUpdateComponent />
      </Provider>
    );

    expect(getByText('EUR/USD')).toBeTruthy();
    expect(getByText('1.1000')).toBeTruthy();
    expect(getByText('1.1002')).toBeTruthy();
  });

  it('should update prices in real-time', async () => {
    const { getByText, rerender } = render(
      <Provider store={store}>
        <PriceUpdateComponent />
      </Provider>
    );

    // Simulate WebSocket price update
    const priceUpdate = {
      type: 'priceUpdate',
      data: {
        symbol: 'EURUSD',
        bid: 1.1005,
        ask: 1.1007,
        timestamp: Date.now()
      }
    };

    // Trigger WebSocket message handler
    const messageHandler = mockWebSocket.on.mock.calls.find(
      call => call[0] === 'message'
    )[1];
    messageHandler(JSON.stringify(priceUpdate));

    // Update store with new price
    store = mockStore({
      market: {
        prices: {
          EURUSD: { bid: 1.1005, ask: 1.1007, change: 0.0010 },
          GBPUSD: { bid: 1.2500, ask: 1.2502, change: -0.0003 }
        },
        subscriptions: ['EURUSD', 'GBPUSD']
      },
      user: {
        authenticated: true
      }
    });

    rerender(
      <Provider store={store}>
        <PriceUpdateComponent />
      </Provider>
    );

    await waitFor(() => {
      expect(getByText('1.1005')).toBeTruthy();
      expect(getByText('1.1007')).toBeTruthy();
    });
  });

  it('should handle WebSocket reconnection', async () => {
    const { getByText } = render(
      <Provider store={store}>
        <PriceUpdateComponent />
      </Provider>
    );

    // Simulate connection loss
    mockWebSocket.readyState = WebSocket.CLOSED;
    const closeHandler = mockWebSocket.on.mock.calls.find(
      call => call[0] === 'close'
    )[1];
    closeHandler();

    // Should show reconnecting status
    await waitFor(() => {
      expect(getByText(/Reconnecting/i)).toBeTruthy();
    });

    // Simulate successful reconnection
    mockWebSocket.readyState = WebSocket.OPEN;
    const openHandler = mockWebSocket.on.mock.calls.find(
      call => call[0] === 'open'
    )[1];
    openHandler();

    // Should resubscribe to pairs
    expect(mockWebSocket.send).toHaveBeenCalledWith(
      JSON.stringify({
        type: 'subscribe',
        pairs: ['EURUSD', 'GBPUSD']
      })
    );
  });

  it('should handle price update animations', async () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <PriceUpdateComponent />
      </Provider>
    );

    const priceElement = getByTestId('price-EURUSD');
    const initialStyle = priceElement.props.style;

    // Simulate price increase
    const priceUpdate = {
      type: 'priceUpdate',
      data: {
        symbol: 'EURUSD',
        bid: 1.1010,
        ask: 1.1012,
        timestamp: Date.now()
      }
    };

    const messageHandler = mockWebSocket.on.mock.calls.find(
      call => call[0] === 'message'
    )[1];
    messageHandler(JSON.stringify(priceUpdate));

    await waitFor(() => {
      const updatedStyle = priceElement.props.style;
      // Should have green highlight for price increase
      expect(updatedStyle.backgroundColor).toContain('green');
    });
  });

  it('should handle subscription management', () => {
    const { getByText } = render(
      <Provider store={store}>
        <PriceUpdateComponent />
      </Provider>
    );

    // Add new subscription
    const addButton = getByText('Add Pair');
    fireEvent.press(addButton);

    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining('subscribe')
    );
  });

  it('should display connection status', () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <PriceUpdateComponent />
      </Provider>
    );

    const statusIndicator = getByTestId('connection-status');
    expect(statusIndicator.props.style.backgroundColor).toBe('green');
  });

  it('should handle price update errors gracefully', async () => {
    const { getByText } = render(
      <Provider store={store}>
        <PriceUpdateComponent />
      </Provider>
    );

    // Simulate error in price update
    const errorHandler = mockWebSocket.on.mock.calls.find(
      call => call[0] === 'error'
    )[1];
    errorHandler(new Error('WebSocket error'));

    await waitFor(() => {
      expect(getByText(/Connection error/i)).toBeTruthy();
    });
  });
});
