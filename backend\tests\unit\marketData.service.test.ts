import { marketDataService } from '../../src/services/marketData.service';
import axios from 'axios';
import { cacheService } from '../../src/services/cache.service';

// Mock dependencies
jest.mock('axios');
jest.mock('../../src/services/cache.service');

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedCache = cacheService as jest.Mocked<typeof cacheService>;

describe('MarketDataService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset environment variables
    process.env.ALPHA_VANTAGE_API_KEY = 'test-api-key';
  });

  describe('fetchForexData', () => {
    const mockPair = 'EURUSD';
    const mockApiResponse = {
      data: {
        'Realtime Currency Exchange Rate': {
          '1. From_Currency Code': 'EUR',
          '2. From_Currency Name': 'Euro',
          '3. To_Currency Code': 'USD',
          '4. To_Currency Name': 'United States Dollar',
          '5. Exchange Rate': '1.10000',
          '6. Last Refreshed': '2024-01-15 10:30:00',
          '7. Time Zone': 'UTC',
          '8. Bid Price': '1.09995',
          '9. Ask Price': '1.10005',
        },
      },
    };

    it('should fetch forex data successfully from API', async () => {
      // Arrange
      mockedCache.get.mockResolvedValue(null);
      mockedAxios.get.mockResolvedValue(mockApiResponse);

      // Act
      const result = await marketDataService.fetchForexData(mockPair);

      // Assert
      expect(mockedCache.get).toHaveBeenCalledWith(`forex:${mockPair}`);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('alphavantage.co/query')
      );
      expect(result).toEqual({
        symbol: mockPair,
        bid: 1.09995,
        ask: 1.10005,
        timestamp: expect.any(Date),
        change: 0,
        changePercent: 0,
      });
    });

    it('should return cached data if available', async () => {
      // Arrange
      const cachedData = JSON.stringify({
        symbol: mockPair,
        bid: 1.09990,
        ask: 1.10010,
        timestamp: new Date(),
        change: 0.0001,
        changePercent: 0.01,
      });
      mockedCache.get.mockResolvedValue(cachedData);

      // Act
      const result = await marketDataService.fetchForexData(mockPair);

      // Assert
      expect(mockedCache.get).toHaveBeenCalledWith(`forex:${mockPair}`);
      expect(mockedAxios.get).not.toHaveBeenCalled();
      expect(result).toEqual(JSON.parse(cachedData));
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      mockedCache.get.mockResolvedValue(null);
      mockedAxios.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await marketDataService.fetchForexData(mockPair);

      // Assert
      expect(result).toBeNull();
    });

    it('should handle rate limit errors', async () => {
      // Arrange
      mockedCache.get.mockResolvedValue(null);
      mockedAxios.get.mockResolvedValue({
        data: {
          Note: 'Thank you for using Alpha Vantage! Our standard API call frequency is 5 calls per minute.',
        },
      });

      // Act
      const result = await marketDataService.fetchForexData(mockPair);

      // Assert
      expect(result).toBeNull();
    });
  });
});
