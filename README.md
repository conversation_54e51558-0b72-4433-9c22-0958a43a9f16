# Forex Trading Platform

A comprehensive forex trading platform with real-time market data, news, analysis tools, and beautiful UI.

## 🚀 Features

- **Real-Time Market Data**: Live forex prices with WebSocket updates
- **Economic News & Calendar**: Stay updated with market-moving events
- **Technical Analysis Tools**: Built-in indicators (MA, RSI, MACD, Bollinger Bands)
- **Beautiful UI/UX**: Dark/light themes with smooth animations
- **User Management**: Secure authentication and personalized watchlists
- **Push Notifications**: Alerts for price movements and economic events

## 📋 Project Structure

```
forex-trading-platform/
├── backend/               # Node.js/Express API server
│   ├── src/              # Source code
│   ├── tests/            # Unit and integration tests
│   └── package.json      # Backend dependencies
├── mobile/               # React Native mobile app
│   ├── src/              # Source code
│   ├── android/          # Android-specific code
│   ├── ios/              # iOS-specific code
│   └── package.json      # Mobile dependencies
└── docs/                 # Documentation
```

## 🛠️ Tech Stack

### Backend
- Node.js with Express
- MongoDB Atlas (Database)
- Firebase (Authentication & Firestore)
- WebSocket for real-time updates
- Redis for caching

### Mobile
- React Native with TypeScript
- Redux Toolkit for state management
- React Navigation for routing
- React Native Chart Kit for visualizations
- Firebase SDK

### APIs
- Alpha Vantage (Forex data)
- Finnhub (Economic calendar)
- FRED API (Economic indicators)

## 🚦 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- React Native development environment
- MongoDB Atlas account
- Firebase project
- API keys for data providers

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/forex-trading-platform.git
cd forex-trading-platform
```

2. Set up the backend
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your API keys
npm run dev
```

3. Set up the mobile app
```bash
cd mobile
npm install
# For iOS
cd ios && pod install
# Run the app
npm run ios # or npm run android
```

## 📝 API Documentation

API documentation is available at `/api/docs` when running the backend server.

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see [LICENSE](LICENSE) file for details.
