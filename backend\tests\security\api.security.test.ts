import request from 'supertest';
import { app } from '../../src/index';
import jwt from 'jsonwebtoken';

describe('API Security Tests', () => {
  const validToken = jwt.sign(
    { id: 'test-user-123', email: '<EMAIL>' },
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn: '1h' }
  );

  describe('Authentication Tests', () => {
    it('should reject requests without authentication token', async () => {
      const response = await request(app)
        .get('/api/v1/market/EURUSD')
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('authorization');
    });

    it('should reject requests with invalid token format', async () => {
      const response = await request(app)
        .get('/api/v1/market/EURUSD')
        .set('Authorization', 'InvalidToken')
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });

    it('should accept requests with valid Bearer token', async () => {
      const response = await request(app)
        .get('/api/v1/market/EURUSD')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
    });

    it('should reject expired tokens', async () => {
      const expiredToken = jwt.sign(
        { id: 'test-user', email: '<EMAIL>' },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '-1h' } // Already expired
      );

      const response = await request(app)
        .get('/api/v1/market/EURUSD')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body.error).toContain('expired');
    });
  });

  describe('Input Validation Tests', () => {
    it('should reject invalid currency pair format', async () => {
      const response = await request(app)
        .get('/api/v1/market/INVALID_PAIR')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(400);

      expect(response.body.error).toContain('Invalid currency pair');
    });

    it('should sanitize user input in search queries', async () => {
      const maliciousInput = '<script>alert("XSS")</script>';
      const response = await request(app)
        .get('/api/v1/news/search')
        .query({ q: maliciousInput })
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      // Response should not contain the script tag
      expect(JSON.stringify(response.body)).not.toContain('<script>');
    });

    it('should reject SQL injection attempts', async () => {
      const sqlInjection = "'; DROP TABLE users; --";
      const response = await request(app)
        .post('/api/v1/watchlist')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ name: sqlInjection })
        .expect(400);

      expect(response.body.error).toBeDefined();
    });

    it('should validate numeric inputs', async () => {
      const response = await request(app)
        .post('/api/v1/analysis/indicators')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          symbol: 'EURUSD',
          period: 'not-a-number', // Should be numeric
          indicator: 'SMA'
        })
        .expect(400);

      expect(response.body.error).toContain('period');
    });
  });

  describe('Rate Limiting Tests', () => {
    it('should enforce rate limits on API endpoints', async () => {
      const requests = [];
      
      // Make 10 rapid requests
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(app)
            .get('/api/v1/market/EURUSD')
            .set('Authorization', `Bearer ${validToken}`)
        );
      }

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited (429 status)
      const rateLimited = responses.filter(r => r.status === 429);
      expect(rateLimited.length).toBeGreaterThan(0);
    });

    it('should include rate limit headers', async () => {
      const response = await request(app)
        .get('/api/v1/market/EURUSD')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.headers).toHaveProperty('x-ratelimit-limit');
      expect(response.headers).toHaveProperty('x-ratelimit-remaining');
    });
  });

  describe('CORS Tests', () => {
    it('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/api/v1/market/EURUSD')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'GET')
        .expect(200);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
      expect(response.headers['access-control-allow-methods']).toContain('GET');
    });

    it('should reject requests from unauthorized origins', async () => {
      const response = await request(app)
        .get('/api/v1/market/EURUSD')
        .set('Authorization', `Bearer ${validToken}`)
        .set('Origin', 'http://malicious-site.com');

      // Check if CORS is properly configured
      const allowedOrigin = response.headers['access-control-allow-origin'];
      expect(allowedOrigin).not.toBe('http://malicious-site.com');
    });
  });

  describe('API Key Security', () => {
    it('should not expose API keys in responses', async () => {
      const response = await request(app)
        .get('/api/v1/config')
        .set('Authorization', `Bearer ${validToken}`);

      const responseString = JSON.stringify(response.body);
      
      // Check for common API key patterns
      expect(responseString).not.toMatch(/api[_-]?key/i);
      expect(responseString).not.toMatch(/secret/i);
      expect(responseString).not.toMatch(/password/i);
    });

    it('should mask sensitive data in error messages', async () => {
      // Trigger an error that might contain sensitive info
      const response = await request(app)
        .post('/api/v1/market/invalid-endpoint')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(404);

      expect(response.body).not.toContain(process.env.ALPHA_VANTAGE_API_KEY);
      expect(response.body).not.toContain(process.env.JWT_SECRET);
    });
  });

  describe('Content Security', () => {
    it('should set security headers', async () => {
      const response = await request(app)
        .get('/api/v1/market/EURUSD')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
    });

    it('should validate content-type on POST requests', async () => {
      const response = await request(app)
        .post('/api/v1/watchlist')
        .set('Authorization', `Bearer ${validToken}`)
        .set('Content-Type', 'text/plain') // Wrong content type
        .send('{"symbol": "EURUSD"}')
        .expect(400);

      expect(response.body.error).toContain('Content-Type');
    });
  });
});

