import request from 'supertest';
import { auth } from '../../src/config/firebase';
import User from '../../src/models/User';
import { app } from '../../src/index';
import mongoose from 'mongoose';

// Mock Firebase Admin
jest.mock('../../../src/config/firebase', () => ({
  auth: jest.fn(() => ({
    verifyIdToken: jest.fn(),
  })),
}));

// Mock User model
jest.mock('../../../src/models/User');

const mockedAuth = auth as jest.MockedFunction<typeof auth>;
const mockedUser = User as jest.Mocked<typeof User>;

describe('Auth Routes Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/register', () => {
    const validRegistrationData = {
      email: '<EMAIL>',
      displayName: 'Test User',
      firebaseUid: 'firebase-uid-123',
    };

    it('should register a new user successfully', async () => {
      // Arrange
      mockedUser.findOne = jest.fn().mockResolvedValue(null);
      mockedUser.create = jest.fn().mockResolvedValue({
        _id: 'user-id-123',
        ...validRegistrationData,
        preferences: {
          theme: 'dark',
          defaultCurrency: 'USD',
          notifications: {
            priceAlerts: true,
            news: true,
            economicEvents: true,
          },
          favoritesPairs: ['EURUSD', 'GBPUSD', 'USDJPY'],
        },
      });

      // Act
      const response = await request(app)
        .post('/api/auth/register')
        .send(validRegistrationData)
        .expect('Content-Type', /json/)
        .expect(201);

      // Assert
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(validRegistrationData.email);
      expect(mockedUser.findOne).toHaveBeenCalledWith({ 
        firebaseUid: validRegistrationData.firebaseUid 
      });
      expect(mockedUser.create).toHaveBeenCalled();
    });

    it('should return existing user if already registered', async () => {
      // Arrange
      const existingUser = {
        _id: 'existing-user-id',
        ...validRegistrationData,
        preferences: { theme: 'light' },
      };
      mockedUser.findOne = jest.fn().mockResolvedValue(existingUser);

      // Act
      const response = await request(app)
        .post('/api/auth/register')
        .send(validRegistrationData)
        .expect(200);

      // Assert
      expect(response.body.user._id).toBe(existingUser._id);
      expect(mockedUser.create).not.toHaveBeenCalled();
    });

    it('should handle missing required fields', async () => {
      // Act
      const response = await request(app)
        .post('/api/auth/register')
        .send({ email: '<EMAIL>' })
        .expect(400);

      // Assert
      expect(response.body).toHaveProperty('error');
    });

    it('should handle database errors', async () => {
      // Arrange
      mockedUser.findOne = jest.fn().mockRejectedValue(new Error('DB Error'));

      // Act
      const response = await request(app)
        .post('/api/auth/register')
        .send(validRegistrationData)
        .expect(500);

      // Assert
      expect(response.body).toHaveProperty('error');
    });

    it('should be rate limited', async () => {
      // Make multiple requests quickly
      const requests = Array(10).fill(null).map(() => 
        request(app)
          .post('/api/auth/register')
          .send(validRegistrationData)
      );

      const responses = await Promise.all(requests);
      
      // Some should be rate limited
      const rateLimited = responses.some(res => res.status === 429);
      expect(rateLimited).toBe(true);
    });
  });

  describe('Protected Routes', () => {
    const mockToken = 'mock-firebase-token';
    const mockDecodedToken = {
      uid: 'firebase-uid-123',
      email: '<EMAIL>',
    };

    it('should verify Firebase token on protected routes', async () => {
      // Arrange
      (mockedAuth().verifyIdToken as jest.Mock).mockResolvedValue(mockDecodedToken);

      // Act
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(200);

      // Assert
      expect(mockedAuth().verifyIdToken).toHaveBeenCalledWith(mockToken);
    });

    it('should reject requests without token', async () => {
      // Act
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401);

      // Assert
      expect(response.body.error).toBe('No token provided');
    });

    it('should reject requests with invalid token', async () => {
      // Arrange
      (mockedAuth().verifyIdToken as jest.Mock).mockRejectedValue(
        new Error('Invalid token')
      );

      // Act
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer invalid-token`)
        .expect(401);

      // Assert
      expect(response.body.error).toBe('Invalid token');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should handle logout request', async () => {
      // Act
      const response = await request(app)
        .post('/api/auth/logout')
        .expect(200);

      // Assert
      expect(response.body.message).toBe('Logged out successfully');
    });
  });
});

