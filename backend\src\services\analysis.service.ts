import { marketDataService } from './marketData.service';
import { logger } from '../utils/logger';

interface IndicatorResult {
  indicator: string;
  value: number | number[];
  signal?: string;
}

export async function calculateIndicators(
  pair: string,
  indicators: string[],
  period: number = 14
): Promise<IndicatorResult[]> {
  try {
    // Fetch historical data
    const historicalData = await marketDataService.fetchHistoricalData(pair, '1D');
    
    if (!historicalData || historicalData.length < period) {
      throw new Error('Insufficient data for calculations');
    }
    
    const results: IndicatorResult[] = [];
    const closes = historicalData.map((d: any) => d.close);
    
    for (const indicator of indicators) {
      switch (indicator.toLowerCase()) {
        case 'sma':
          results.push({
            indicator: 'SMA',
            value: calculateSMA(closes, period),
          });
          break;
          
        case 'ema':
          results.push({
            indicator: 'EMA',
            value: calculateEMA(closes, period),
          });
          break;
          
        case 'rsi':
          const rsiValue = calculateRSI(closes, period);
          results.push({
            indicator: 'RSI',
            value: rsiValue,
            signal: getRSISignal(rsiValue),
          });
          break;
          
        case 'macd':
          const macdResult = calculateMACD(closes);
          results.push({
            indicator: 'MACD',
            value: macdResult,
          });
          break;
      }
    }
    
    return results;
  } catch (error) {
    logger.error('Error calculating indicators:', error);
    throw error;
  }
}

function calculateSMA(values: number[], period: number): number {
  const recent = values.slice(-period);
  return recent.reduce((sum, val) => sum + val, 0) / period;
}

function calculateEMA(values: number[], period: number): number {
  const k = 2 / (period + 1);
  let ema = values[0];
  
  for (let i = 1; i < values.length; i++) {
    ema = values[i] * k + ema * (1 - k);
  }
  
  return ema;
}

function calculateRSI(values: number[], period: number): number {
  const gains: number[] = [];
  const losses: number[] = [];
  
  for (let i = 1; i < values.length; i++) {
    const diff = values[i] - values[i - 1];
    if (diff > 0) {
      gains.push(diff);
      losses.push(0);
    } else {
      gains.push(0);
      losses.push(Math.abs(diff));
    }
  }
  
  const avgGain = gains.slice(-period).reduce((sum, g) => sum + g, 0) / period;
  const avgLoss = losses.slice(-period).reduce((sum, l) => sum + l, 0) / period;
  
  if (avgLoss === 0) return 100;
  
  const rs = avgGain / avgLoss;
  return 100 - (100 / (1 + rs));
}

function getRSISignal(rsi: number): string {
  if (rsi > 70) return 'Overbought';
  if (rsi < 30) return 'Oversold';
  return 'Neutral';
}

function calculateMACD(values: number[]): number[] {
  const ema12 = calculateEMA(values, 12);
  const ema26 = calculateEMA(values, 26);
  const macd = ema12 - ema26;
  const signal = calculateEMA([macd], 9);
  
  return [macd, signal, macd - signal];
}
