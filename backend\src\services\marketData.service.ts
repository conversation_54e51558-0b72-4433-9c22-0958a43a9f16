import axios from 'axios';
import cron from 'node-cron';
import { Server } from 'socket.io';
import { logger } from '../utils/logger';
import { cacheService } from './cache.service';

interface ForexPair {
  symbol: string;
  bid: number;
  ask: number;
  timestamp: Date;
  change: number;
  changePercent: number;
}

class MarketDataService {
  private io: Server | null = null;
  private watchedPairs: Set<string> = new Set(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD']);
  
  setSocketServer(io: Server) {
    this.io = io;
  }

  async fetchForexData(pair: string): Promise<ForexPair | null> {
    try {
      // Check cache first
      const cached = await cacheService.get(`forex:${pair}`);
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(
        `https://www.alphavantage.co/query?function=CURRENCY_EXCHANGE_RATE&from_currency=${pair.slice(0, 3)}&to_currency=${pair.slice(3, 6)}&apikey=${process.env.ALPHA_VANTAGE_API_KEY}`
      );

      if (response.data['Realtime Currency Exchange Rate']) {
        const data = response.data['Realtime Currency Exchange Rate'];
        const forexData: ForexPair = {
          symbol: pair,
          bid: parseFloat(data['8. Bid Price']),
          ask: parseFloat(data['9. Ask Price']),
          timestamp: new Date(),
          change: 0, // Calculate from previous data
          changePercent: 0, // Calculate from previous data
        };

        // Cache for 1 minute
        await cacheService.set(`forex:${pair}`, JSON.stringify(forexData), 60);
        
        return forexData;
      }
      
      return null;
    } catch (error) {
      logger.error(`Error fetching forex data for ${pair}:`, error);
      return null;
    }
  }

  async fetchHistoricalData(pair: string, interval: string = '1D'): Promise<any[]> {
    try {
      // Check cache first
      const cached = await cacheService.get(`history:${pair}:${interval}`);
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(
        `https://www.alphavantage.co/query?function=FX_DAILY&from_symbol=${pair.slice(0, 3)}&to_symbol=${pair.slice(3, 6)}&apikey=${process.env.ALPHA_VANTAGE_API_KEY}`
      );

      if (response.data['Time Series FX (Daily)']) {
        const timeSeries = response.data['Time Series FX (Daily)'];
        const chartData = Object.entries(timeSeries).map(
          ([date, values]: [string, any]) => ({
            date,
            open: parseFloat(values['1. open']),
            high: parseFloat(values['2. high']),
            low: parseFloat(values['3. low']),
            close: parseFloat(values['4. close']),
          })
        ).reverse();

        // Cache for 1 hour
        await cacheService.set(`history:${pair}:${interval}`, JSON.stringify(chartData), 3600);

        return chartData;
      }

      return [];
    } catch (error) {
      logger.error(`Error fetching historical data for ${pair}:`, error);
      return [];
    }
  }

  async broadcastPriceUpdates(): Promise<void> {
    if (!this.io) return;

    try {
      const pairs = Array.from(this.watchedPairs);
      
      for (const pair of pairs) {
        const priceData = await this.fetchForexData(pair);
        if (priceData) {
          this.io.to(`price-${pair}`).emit('priceUpdate', priceData);
        }
      }
    } catch (error) {
      logger.error('Error broadcasting price updates:', error);
    }
  }
}

export const marketDataService = new MarketDataService();

export const startMarketDataJobs = (io: Server) => {
  marketDataService.setSocketServer(io);
  
  // Update prices every 30 seconds
  cron.schedule('*/30 * * * * *', async () => {
    await marketDataService.broadcastPriceUpdates();
  });
  
  logger.info('Market data jobs started');
};
