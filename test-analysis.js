const fs = require('fs');
const path = require('path');

console.log('======================================');
console.log('Forex Trading Platform Test Report');
console.log('======================================\n');

// Check environment
console.log('1. Environment Check:');
console.log('--------------------');
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);
console.log('Working directory:', __dirname);
console.log('');

// Check backend
console.log('2. Backend Analysis:');
console.log('--------------------');
const backendPath = path.join(__dirname, 'backend');
console.log('Backend dependencies:', fs.existsSync(path.join(backendPath, 'node_modules')) ? '✓ Installed' : '✗ Not installed');
console.log('Backend .env:', fs.existsSync(path.join(backendPath, '.env')) ? '✓ Exists' : '✗ Missing');
console.log('Backend entry:', fs.existsSync(path.join(backendPath, 'src/index.ts')) ? '✓ Exists' : '✗ Missing');

// List backend files
console.log('\nBackend source structure:');
const backendSrc = path.join(backendPath, 'src');
if (fs.existsSync(backendSrc)) {
    const dirs = fs.readdirSync(backendSrc).filter(f => fs.statSync(path.join(backendSrc, f)).isDirectory());
    dirs.forEach(dir => console.log(`  - ${dir}/`));
}
console.log('');

// Check mobile
console.log('3. Mobile App Analysis:');
console.log('--------------------');
const mobilePath = path.join(__dirname, 'mobile');
console.log('Mobile dependencies:', fs.existsSync(path.join(mobilePath, 'node_modules')) ? '✓ Installed' : '✗ Not installed');
console.log('Mobile entry:', fs.existsSync(path.join(mobilePath, 'App.tsx')) ? '✓ Exists' : '✗ Missing');

// List mobile files
console.log('\nMobile source structure:');
const mobileSrc = path.join(mobilePath, 'src');
if (fs.existsSync(mobileSrc)) {
    const dirs = fs.readdirSync(mobileSrc).filter(f => fs.statSync(path.join(mobileSrc, f)).isDirectory());
    dirs.forEach(dir => console.log(`  - ${dir}/`));
}
console.log('');

// Check critical features
console.log('4. Key Features Status:');
console.log('--------------------');
const features = [
    { name: 'Auth Service', path: 'backend/src/services/auth.service.ts' },
    { name: 'Market Data Service', path: 'backend/src/services/marketData.service.ts' },
    { name: 'WebSocket Server', path: 'backend/src/services/websocket.service.ts' },
    { name: 'Dashboard Screen', path: 'mobile/src/screens/DashboardScreen.tsx' },
    { name: 'Login Screen', path: 'mobile/src/screens/LoginScreen.tsx' },
    { name: 'Redux Store', path: 'mobile/src/store/index.ts' }
];

features.forEach(feature => {
    const exists = fs.existsSync(path.join(__dirname, feature.path));
    console.log(`${feature.name}: ${exists ? '✓ Implemented' : '✗ Not found'}`);
});

console.log('\n======================================');
console.log('Summary:');
console.log('- Backend: Express + TypeScript + MongoDB');
console.log('- Mobile: React Native + Expo + Redux');
console.log('- APIs: Alpha Vantage, Finnhub, FRED');
console.log('- Real-time: Socket.io WebSocket');
console.log('======================================');
