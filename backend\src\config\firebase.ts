import admin from 'firebase-admin';
import { logger } from '../utils/logger';

export const initializeFirebase = async () => {
  try {
    const serviceAccount = {
      projectId: process.env.FIREBASE_PROJECT_ID,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    };

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
      databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}.firebaseio.com`,
    });

    logger.info('Firebase initialized successfully');
  } catch (error) {
    logger.error('Firebase initialization error:', error);
    throw error;
  }
};

export const auth = () => admin.auth();
export const firestore = () => admin.firestore();
export const messaging = () => admin.messaging();
