import { Router } from 'express';
import { newsService } from '../services/news.service';
import { apiRateLimiter } from '../middleware/rateLimiter';

const router = Router();

// Get news articles
router.get('/', apiRateLimiter, async (req, res) => {
  try {
    const { category, search, page = 1, limit = 20 } = req.query;
    
    const result = await newsService.getNews(
      { category, search },
      parseInt(page as string),
      parseInt(limit as string)
    );
    
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch news' });
  }
});

// Get economic calendar
router.get('/calendar', apiRateLimiter, async (_req, res) => {
  try {
    const events = await newsService.fetchEconomicCalendar();
    res.json({ events });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch economic calendar' });
  }
});

// Refresh news (manual trigger)
router.post('/refresh', apiRateLimiter, async (_req, res) => {
  try {
    await newsService.fetchForexNews();
    res.json({ message: 'News refresh initiated' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to refresh news' });
  }
});

export default router;
