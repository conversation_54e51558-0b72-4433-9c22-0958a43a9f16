import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import marketDataReducer from './slices/marketDataSlice';
import newsReducer from './slices/newsSlice';
import watchlistReducer from './slices/watchlistSlice';
import settingsReducer from './slices/settingsSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    marketData: marketDataReducer,
    news: newsReducer,
    watchlist: watchlistReducer,
    settings: settingsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['marketData/updatePrices'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
