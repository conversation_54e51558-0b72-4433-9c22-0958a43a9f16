import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { configureStore } from '@reduxjs/toolkit';
import DashboardScreen from '../src/screens/DashboardScreen';
import { marketDataSlice } from '../src/store/slices/marketDataSlice';
import { authSlice } from '../src/store/slices/authSlice';
import { apiClient } from '../src/services/api';

// Mock dependencies
jest.mock('../src/services/api');
jest.mock('../src/services/websocket', () => ({
  connectWebSocket: jest.fn(),
  subscribeToPair: jest.fn(),
  unsubscribeFromPair: jest.fn(),
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Helper function to create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      marketData: marketDataSlice.reducer,
      auth: authSlice.reducer,
    },
    preloadedState: {
      marketData: {
        prices: {},
        watchedPairs: ['EURUSD', 'GBPUSD'],
        loading: false,
        error: null,
      },
      auth: {
        user: { id: 'test-user', email: '<EMAIL>' },
        isAuthenticated: true,
        loading: false,
      },
      ...initialState,
    },
  });
};

// Helper component wrapper
const TestWrapper = ({ children, store }) => (
  <Provider store={store}>
    <NavigationContainer>
      {children}
    </NavigationContainer>
  </Provider>
);

describe('DashboardScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading state initially', () => {
    const store = createTestStore({
      marketData: {
        prices: {},
        watchedPairs: ['EURUSD'],
        loading: true,
        error: null,
      },
    });

    const { getByTestId } = render(
      <TestWrapper store={store}>
        <DashboardScreen />
      </TestWrapper>
    );

    expect(getByTestId('loading-indicator')).toBeTruthy();
  });

  it('should display price cards for watched pairs', async () => {
    const mockPrices = {
      EURUSD: {
        symbol: 'EURUSD',
        bid: 1.0999,
        ask: 1.1001,
        timestamp: new Date().toISOString(),
        change: 0.0005,
        changePercent: 0.05,
      },
      GBPUSD: {
        symbol: 'GBPUSD',
        bid: 1.2550,
        ask: 1.2552,
        timestamp: new Date().toISOString(),
        change: -0.0010,
        changePercent: -0.08,
      },
    };

    mockApiClient.post.mockResolvedValue({
      data: { data: mockPrices },
    });

    const store = createTestStore({
      marketData: {
        prices: mockPrices,
        watchedPairs: ['EURUSD', 'GBPUSD'],
        loading: false,
        error: null,
      },
    });

    const { getByText } = render(
      <TestWrapper store={store}>
        <DashboardScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(getByText('EURUSD')).toBeTruthy();
      expect(getByText('GBPUSD')).toBeTruthy();
      expect(getByText('1.09990')).toBeTruthy();
      expect(getByText('1.25500')).toBeTruthy();
    });
  });

  it('should handle refresh action', async () => {
    const store = createTestStore();
    const { getByTestId } = render(
      <TestWrapper store={store}>
        <DashboardScreen />
      </TestWrapper>
    );

    const refreshControl = getByTestId('refresh-control');
    fireEvent(refreshControl, 'refresh');

    expect(mockApiClient.post).toHaveBeenCalledWith('/market-data/prices', {
      pairs: ['EURUSD', 'GBPUSD'],
    });
  });
});