import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SettingsState {
  isDarkMode: boolean;
  defaultCurrency: string;
  notifications: {
    priceAlerts: boolean;
    news: boolean;
    economicEvents: boolean;
  };
  hapticFeedback: boolean;
}

const initialState: SettingsState = {
  isDarkMode: true,
  defaultCurrency: 'USD',
  notifications: {
    priceAlerts: true,
    news: true,
    economicEvents: true,
  },
  hapticFeedback: true,
};

export const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    toggleDarkMode: (state) => {
      state.isDarkMode = !state.isDarkMode;
    },
    setDefaultCurrency: (state, action: PayloadAction<string>) => {
      state.defaultCurrency = action.payload;
    },
    updateNotificationSettings: (
      state,
      action: PayloadAction<Partial<SettingsState['notifications']>>
    ) => {
      state.notifications = { ...state.notifications, ...action.payload };
    },
    toggleHapticFeedback: (state) => {
      state.hapticFeedback = !state.hapticFeedback;
    },
  },
});

export const {
  toggleDarkMode,
  setDefaultCurrency,
  updateNotificationSettings,
  toggleHapticFeedback,
} = settingsSlice.actions;

export default settingsSlice.reducer;
