Write-Host "======================================" -ForegroundColor Cyan
Write-Host "Forex Trading Platform Test Report" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host ""

# Check Node.js and npm
Write-Host "1. Environment Check:" -ForegroundColor Yellow
Write-Host "--------------------"
Write-Host "Node.js version: " -NoNewline
node --version
Write-Host "npm version: " -NoNewline
npm --version
Write-Host ""

# Check backend
Write-Host "2. Backend Analysis:" -ForegroundColor Yellow
Write-Host "--------------------"
Set-Location backend
if (Test-Path node_modules) {
    Write-Host "✓ Backend dependencies installed" -ForegroundColor Green
} else {
    Write-Host "✗ Backend dependencies not installed" -ForegroundColor Red
}

Write-Host ""
Write-Host "Backend source files:"
Get-ChildItem -Path src -Filter "*.ts" -Recurse | Select-Object -First 10 | ForEach-Object { $_.FullName.Replace((Get-Location).Path, ".")}
Write-Host ""

# Check mobile
Write-Host "3. Mobile App Analysis:" -ForegroundColor Yellow
Write-Host "--------------------"
Set-Location ../mobile
if (Test-Path node_modules) {
    Write-Host "✓ Mobile dependencies installed" -ForegroundColor Green
} else {
    Write-Host "✗ Mobile dependencies not installed" -ForegroundColor Red
}

Write-Host ""
Write-Host "Mobile source files:"
Get-ChildItem -Path src -Filter "*.tsx","*.ts" -Recurse | Select-Object -First 10 | ForEach-Object { $_.FullName.Replace((Get-Location).Path, ".")}
Write-Host ""

# Check critical files
Write-Host "4. Critical Files Check:" -ForegroundColor Yellow
Write-Host "--------------------"
Set-Location ..

if (Test-Path "backend/.env") {
    Write-Host "✓ Backend .env exists" -ForegroundColor Green
} else {
    Write-Host "✗ Backend .env missing" -ForegroundColor Red
}

if (Test-Path "backend/src/index.ts") {
    Write-Host "✓ Backend entry point exists" -ForegroundColor Green
} else {
    Write-Host "✗ Backend entry point missing" -ForegroundColor Red
}

if (Test-Path "mobile/App.tsx") {
    Write-Host "✓ Mobile entry point exists" -ForegroundColor Green
} else {
    Write-Host "✗ Mobile entry point missing" -ForegroundColor Red
}

if (Test-Path "mobile/src/screens/DashboardScreen.tsx") {
    Write-Host "✓ Dashboard screen exists" -ForegroundColor Green
} else {
    Write-Host "✗ Dashboard screen missing" -ForegroundColor Red
}

Write-Host ""
Write-Host "5. Test Coverage:" -ForegroundColor Yellow
Write-Host "--------------------"
$backendTests = (Get-ChildItem -Path "backend/tests" -Filter "*.test.ts","*.spec.ts" -Recurse -ErrorAction SilentlyContinue).Count
$mobileTests = (Get-ChildItem -Path "mobile/__tests__" -Filter "*.test.tsx","*.test.ts" -Recurse -ErrorAction SilentlyContinue).Count
Write-Host "Backend test files: $backendTests"
Write-Host "Mobile test files: $mobileTests"

Write-Host ""
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "Test Complete" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
