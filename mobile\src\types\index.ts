export interface User {
  id: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  preferences: {
    theme: 'light' | 'dark';
    defaultCurrency: string;
    notifications: {
      priceAlerts: boolean;
      news: boolean;
      economicEvents: boolean;
    };
    favoritesPairs: string[];
  };
  subscription: {
    plan: 'free' | 'premium';
    expiresAt?: Date;
  };
}

export interface ForexPair {
  symbol: string;
  bid: number;
  ask: number;
  timestamp: Date;
  change: number;
  changePercent: number;
}

export interface NewsArticle {
  id: string;
  title: string;
  summary: string;
  url: string;
  source: string;
  category: string;
  publishedAt: Date;
  image?: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
  impact?: 'high' | 'medium' | 'low';
}

export interface Watchlist {
  id: string;
  userId: string;
  name: string;
  description?: string;
  pairs: string[];
  alerts: Alert[];
  isDefault: boolean;
}

export interface Alert {
  id: string;
  pair: string;
  type: 'price_above' | 'price_below' | 'percentage_change';
  value: number;
  enabled: boolean;
  triggeredAt?: Date;
}

export interface ChartData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
}

export interface Indicator {
  name: string;
  value: number | number[];
  signal?: string;
}

export interface EconomicEvent {
  id: string;
  title: string;
  country: string;
  impact: 'high' | 'medium' | 'low';
  actual?: number;
  forecast?: number;
  previous?: number;
  time: Date;
}
