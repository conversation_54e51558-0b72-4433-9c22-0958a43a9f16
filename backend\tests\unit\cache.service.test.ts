import { cacheService } from '../../src/services/cache.service';
import { createClient } from 'redis';
import { logger } from '../../src/utils/logger';

// Mock dependencies
jest.mock('redis');
jest.mock('../../../src/utils/logger');

const mockedCreateClient = createClient as jest.MockedFunction<typeof createClient>;
const mockedLogger = logger as jest.Mocked<typeof logger>;

describe('CacheService', () => {
  let mockRedisClient: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mock Redis client
    mockRedisClient = {
      connect: jest.fn().mockResolvedValue(undefined),
      get: jest.fn(),
      set: jest.fn().mockResolvedValue('OK'),
      setEx: jest.fn().mockResolvedValue('OK'),
      del: jest.fn().mockResolvedValue(1),
      quit: jest.fn().mockResolvedValue(undefined),
      on: jest.fn(),
    };

    mockedCreateClient.mockReturnValue(mockRedisClient as any);
  });

  describe('connect', () => {
    it('should connect to Redis successfully', async () => {
      // Act
      await cacheService.connect();

      // Assert
      expect(mockedCreateClient).toHaveBeenCalledWith({
        url: 'redis://localhost:6379',
      });
      expect(mockRedisClient.connect).toHaveBeenCalled();
      expect(mockRedisClient.on).toHaveBeenCalledWith('error', expect.any(Function));
      expect(mockRedisClient.on).toHaveBeenCalledWith('connect', expect.any(Function));
    });

    it('should handle connection errors gracefully', async () => {
      // Arrange
      mockRedisClient.connect.mockRejectedValue(new Error('Connection failed'));

      // Act
      await cacheService.connect();

      // Assert
      expect(mockedLogger.error).toHaveBeenCalledWith(
        'Failed to connect to Redis:',
        expect.any(Error)
      );
    });

    it('should use custom Redis URL from environment', async () => {
      // Arrange
      const customUrl = 'redis://custom-host:6380';
      process.env.REDIS_URL = customUrl;

      // Act
      await cacheService.connect();

      // Assert
      expect(mockedCreateClient).toHaveBeenCalledWith({
        url: customUrl,
      });

      // Cleanup
      delete process.env.REDIS_URL;
    });
  });

  describe('get', () => {
    it('should get value from Redis', async () => {
      // Arrange
      const key = 'test-key';
      const value = 'test-value';
      mockRedisClient.get.mockResolvedValue(value);
      
      // Connect first
      await cacheService.connect();
      // Simulate connected state
      (cacheService as any).isConnected = true;

      // Act
      const result = await cacheService.get(key);

      // Assert
      expect(mockRedisClient.get).toHaveBeenCalledWith(key);
      expect(result).toBe(value);
    });

    it('should return null when not connected', async () => {
      // Arrange
      (cacheService as any).isConnected = false;

      // Act
      const result = await cacheService.get('any-key');

      // Assert
      expect(result).toBeNull();
      expect(mockRedisClient.get).not.toHaveBeenCalled();
    });

    it('should handle get errors gracefully', async () => {
      // Arrange
      mockRedisClient.get.mockRejectedValue(new Error('Get failed'));
      await cacheService.connect();
      (cacheService as any).isConnected = true;

      // Act
      const result = await cacheService.get('test-key');

      // Assert
      expect(result).toBeNull();
      expect(mockedLogger.error).toHaveBeenCalledWith(
        'Redis get error:',
        expect.any(Error)
      );
    });
  });

  describe('set', () => {
    it('should set value in Redis without expiration', async () => {
      // Arrange
      const key = 'test-key';
      const value = 'test-value';
      await cacheService.connect();
      (cacheService as any).isConnected = true;

      // Act
      const result = await cacheService.set(key, value);

      // Assert
      expect(mockRedisClient.set).toHaveBeenCalledWith(key, value);
      expect(result).toBe(true);
    });

    it('should set value with expiration', async () => {
      // Arrange
      const key = 'test-key';
      const value = 'test-value';
      const expiration = 3600;
      await cacheService.connect();
      (cacheService as any).isConnected = true;

      // Act
      const result = await cacheService.set(key, value, expiration);

      // Assert
      expect(mockRedisClient.setEx).toHaveBeenCalledWith(key, expiration, value);
      expect(result).toBe(true);
    });

    it('should return false when not connected', async () => {
      // Arrange
      (cacheService as any).isConnected = false;

      // Act
      const result = await cacheService.set('key', 'value');

      // Assert
      expect(result).toBe(false);
      expect(mockRedisClient.set).not.toHaveBeenCalled();
    });

    it('should handle set errors gracefully', async () => {
      // Arrange
      mockRedisClient.set.mockRejectedValue(new Error('Set failed'));
      await cacheService.connect();
      (cacheService as any).isConnected = true;

      // Act
      const result = await cacheService.set('key', 'value');

      // Assert
      expect(result).toBe(false);
      expect(mockedLogger.error).toHaveBeenCalledWith(
        'Redis set error:',
        expect.any(Error)
      );
    });
  });

  describe('del', () => {
    it('should delete key from Redis', async () => {
      // Arrange
      const key = 'test-key';
      await cacheService.connect();
      (cacheService as any).isConnected = true;

      // Act
      const result = await cacheService.del(key);

      // Assert
      expect(mockRedisClient.del).toHaveBeenCalledWith(key);
      expect(result).toBe(true);
    });

    it('should return false when not connected', async () => {
      // Arrange
      (cacheService as any).isConnected = false;

      // Act
      const result = await cacheService.del('key');

      // Assert
      expect(result).toBe(false);
      expect(mockRedisClient.del).not.toHaveBeenCalled();
    });

    it('should handle delete errors gracefully', async () => {
      // Arrange
      mockRedisClient.del.mockRejectedValue(new Error('Delete failed'));
      await cacheService.connect();
      (cacheService as any).isConnected = true;

      // Act
      const result = await cacheService.del('key');

      // Assert
      expect(result).toBe(false);
      expect(mockedLogger.error).toHaveBeenCalledWith(
        'Redis delete error:',
        expect.any(Error)
      );
    });
  });

  describe('disconnect', () => {
    it('should disconnect from Redis', async () => {
      // Arrange
      await cacheService.connect();
      (cacheService as any).isConnected = true;

      // Act
      await cacheService.disconnect();

      // Assert
      expect(mockRedisClient.quit).toHaveBeenCalled();
      expect((cacheService as any).isConnected).toBe(false);
    });

    it('should handle disconnect when not connected', async () => {
      // Arrange
      (cacheService as any).client = null;

      // Act
      await cacheService.disconnect();

      // Assert
      expect(mockRedisClient.quit).not.toHaveBeenCalled();
    });
  });

  describe('Redis event handlers', () => {
    it('should handle Redis error events', async () => {
      // Arrange
      await cacheService.connect();
      const errorHandler = mockRedisClient.on.mock.calls.find(
        (call: any) => call[0] === 'error'
      )[1];

      // Act
      const error = new Error('Redis error');
      errorHandler(error);

      // Assert
      expect(mockedLogger.error).toHaveBeenCalledWith('Redis Client Error:', error);
      expect((cacheService as any).isConnected).toBe(false);
    });

    it('should handle Redis connect events', async () => {
      // Arrange
      await cacheService.connect();
      const connectHandler = mockRedisClient.on.mock.calls.find(
        (call: any) => call[0] === 'connect'
      )[1];

      // Act
      connectHandler();

      // Assert
      expect(mockedLogger.info).toHaveBeenCalledWith('Redis Client Connected');
      expect((cacheService as any).isConnected).toBe(true);
    });
  });
});

