import { Router } from 'express';
import { auth } from '../config/firebase';
import User from '../models/User';
import { logger } from '../utils/logger';
import { authRateLimiter } from '../middleware/rateLimiter';

const router = Router();

// Verify Firebase token middleware
const verifyToken = async (req: any, res: any, next: any) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decodedToken = await auth().verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    logger.error('Token verification error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Register/Login endpoint
router.post('/register', authRateLimiter, async (req, res) => {
  try {
    const { email, displayName, firebaseUid } = req.body;

    // Check if user already exists
    let user = await User.findOne({ firebaseUid });
    
    if (!user) {
      // Create new user
      user = await User.create({
        firebaseUid,
        email,
        displayName,
        preferences: {
          theme: 'dark',
          defaultCurrency: 'USD',
          notifications: {
            priceAlerts: true,
            news: true,
            economicEvents: true,
          },
          favoritesPairs: ['EURUSD', 'GBPUSD', 'USDJPY'],
        },
      });
    } else {
      // Update last login
      user.lastLogin = new Date();
      await user.save();
    }

    res.json({ user });
  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({ error: 'Failed to register user' });
  }
});

// Get current user
router.get('/me', verifyToken, async (req: any, res) => {
  try {
    const user = await User.findOne({ firebaseUid: req.user.uid });
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    return res.json({ user });
  } catch (error) {
    logger.error('Get user error:', error);
    return res.status(500).json({ error: 'Failed to get user' });
  }
});

export default router;
