import { Router } from 'express';
import { calculateIndicators } from '../services/analysis.service';
import { apiRateLimiter } from '../middleware/rateLimiter';

const router = Router();

// Calculate technical indicators
router.post('/indicators', apiRateLimiter, async (req, res) => {
  try {
    const { pair, indicators, period = 14 } = req.body;
    
    if (!pair || !indicators || !Array.isArray(indicators)) {
      return res.status(400).json({ 
        error: 'Pair and indicators array are required' 
      });
    }
    
    const results = await calculateIndicators(pair, indicators, period);
    res.json({ data: results });
  } catch (error) {
    res.status(500).json({ error: 'Failed to calculate indicators' });
  }
});

// Get currency strength
router.get('/strength', apiRateLimiter, async (req, res) => {
  try {
    // Placeholder for currency strength calculation
    const strengthData = {
      USD: 75,
      EUR: 65,
      GBP: 70,
      JPY: 55,
      CHF: 60,
      AUD: 50,
      CAD: 45,
      NZD: 40,
    };
    
    res.json({ data: strengthData });
  } catch (error) {
    res.status(500).json({ error: 'Failed to calculate currency strength' });
  }
});

export default router;
