import mongoose, { Document, Schema } from 'mongoose';

export interface INews extends Document {
  title: string;
  summary: string;
  url: string;
  source: string;
  category: string;
  publishedAt: Date;
  image?: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
  tags?: string[];
  impact?: 'high' | 'medium' | 'low';
  createdAt: Date;
  updatedAt: Date;
}

const NewsSchema = new Schema<INews>({
  title: {
    type: String,
    required: true,
    index: true,
  },
  summary: {
    type: String,
    required: true,
  },
  url: {
    type: String,
    required: true,
    unique: true,
  },
  source: {
    type: String,
    required: true,
  },
  category: {
    type: String,
    required: true,
    enum: ['forex', 'economic', 'crypto', 'general'],
    index: true,
  },
  publishedAt: {
    type: Date,
    required: true,
    index: true,
  },
  image: String,
  sentiment: {
    type: String,
    enum: ['positive', 'negative', 'neutral'],
  },
  tags: [String],
  impact: {
    type: String,
    enum: ['high', 'medium', 'low'],
  },
}, {
  timestamps: true,
});

// Indexes for efficient querying
NewsSchema.index({ publishedAt: -1 });
NewsSchema.index({ category: 1, publishedAt: -1 });
NewsSchema.index({ title: 'text', summary: 'text' });

export default mongoose.model<INews>('News', NewsSchema);
