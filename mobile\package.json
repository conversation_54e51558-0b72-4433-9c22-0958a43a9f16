{"name": "ForexTradingApp", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "lint": "eslint . --ext .ts,.tsx"}, "dependencies": {"expo": "~49.0.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/stack": "^6.3.20", "react-native-screens": "~3.25.0", "react-native-safe-area-context": "4.6.3", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "axios": "^1.6.2", "socket.io-client": "^4.6.1", "react-native-chart-kit": "^6.12.0", "react-native-svg": "13.9.0", "@react-native-async-storage/async-storage": "1.18.2", "expo-notifications": "~0.20.1", "expo-haptics": "~12.4.0", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "@react-native-firebase/app": "^18.7.3", "@react-native-firebase/auth": "^18.7.3", "@react-native-firebase/firestore": "^18.7.3", "react-native-vector-icons": "^10.0.2", "react-native-paper": "^5.11.1", "date-fns": "^2.30.0", "react-native-skeleton-placeholder": "^5.2.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "^0.72.5", "typescript": "^5.1.3", "@typescript-eslint/eslint-plugin": "^6.7.5", "@typescript-eslint/parser": "^6.7.5", "eslint": "^8.50.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.2.1", "@testing-library/react-native": "^12.3.0"}, "private": true}