import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';

export default function RegisterScreen() {
  const navigation = useNavigation<any>();

  return (
    <View style={styles.container}>
      <Text variant="headlineMedium">Register Screen</Text>
      <Text variant="bodyLarge" style={styles.text}>
        Registration functionality coming soon!
      </Text>
      <Button 
        mode="contained" 
        onPress={() => navigation.goBack()}
        style={styles.button}
      >
        Back to Login
      </Button>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    marginVertical: 20,
    textAlign: 'center',
  },
  button: {
    marginTop: 20,
  },
});
