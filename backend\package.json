{"name": "forex-trading-backend", "version": "1.0.0", "description": "Backend API for Forex Trading Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:security": "jest tests/security", "test:performance": "jest tests/performance", "test:ci": "jest --ci --coverage --maxWorkers=2", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\""}, "keywords": ["forex", "trading", "api", "websocket"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "firebase-admin": "^12.0.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "node-cron": "^3.0.3", "redis": "^4.6.11", "socket.io": "^4.6.1", "winston": "^3.11.0"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.3", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "socket.io-client": "^4.8.1", "supertest": "^7.1.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}