import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { NewsArticle } from '../../types';

interface NewsState {
  articles: NewsArticle[];
  economicCalendar: any[];
  loading: boolean;
  error: string | null;
}

const initialState: NewsState = {
  articles: [],
  economicCalendar: [],
  loading: false,
  error: null,
};

export const newsSlice = createSlice({
  name: 'news',
  initialState,
  reducers: {
    setArticles: (state, action: PayloadAction<NewsArticle[]>) => {
      state.articles = action.payload;
    },
    setEconomicCalendar: (state, action: PayloadAction<any[]>) => {
      state.economicCalendar = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setArticles, setEconomicCalendar, setLoading, setError } = newsSlice.actions;
export default newsSlice.reducer;
