const express = require('express');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Test endpoints
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'Forex Trading Platform API is running!',
    version: '1.0.0',
    endpoints: ['/api/market-data/prices', '/api/news/latest', '/api/analysis/indicators']
  });
});

// Mock forex prices
app.get('/api/market-data/prices', (req, res) => {
  const prices = {
    EURUSD: { 
      symbol: 'EURUSD',
      bid: 1.0950 + (Math.random() - 0.5) * 0.001, 
      ask: 1.0952 + (Math.random() - 0.5) * 0.001,
      timestamp: new Date().toISOString()
    },
    GBPUSD: { 
      symbol: 'GBPUSD',
      bid: 1.2850 + (Math.random() - 0.5) * 0.001, 
      ask: 1.2852 + (Math.random() - 0.5) * 0.001,
      timestamp: new Date().toISOString()
    },
    USDJPY: { 
      symbol: 'USDJPY',
      bid: 145.50 + (Math.random() - 0.5) * 0.1, 
      ask: 145.52 + (Math.random() - 0.5) * 0.1,
      timestamp: new Date().toISOString()
    },
    AUDUSD: { 
      symbol: 'AUDUSD',
      bid: 0.6750 + (Math.random() - 0.5) * 0.001, 
      ask: 0.6752 + (Math.random() - 0.5) * 0.001,
      timestamp: new Date().toISOString()
    }
  };
  res.json(prices);
});

// Mock news
app.get('/api/news/latest', (req, res) => {
  res.json({
    news: [
      { 
        id: 1, 
        title: "Fed Keeps Rates Unchanged", 
        summary: "Federal Reserve maintains interest rates at current levels",
        category: "central_banks",
        importance: "high",
        timestamp: new Date().toISOString()
      },
      { 
        id: 2, 
        title: "EUR Strengthens on ECB Comments", 
        summary: "Euro gains following hawkish ECB statements",
        category: "forex",
        importance: "medium", 
        timestamp: new Date().toISOString()
      }
    ]
  });
});

// Mock technical indicators
app.get('/api/analysis/indicators', (req, res) => {
  res.json({
    EURUSD: {
      RSI: 55.3,
      MACD: { value: 0.0012, signal: 0.0010, histogram: 0.0002 },
      SMA_50: 1.0945,
      SMA_200: 1.0890
    }
  });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`\n🚀 Forex Trading Platform Test Server`);
  console.log(`====================================`);
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log(`\nAvailable endpoints:`);
  console.log(`  GET /health`);
  console.log(`  GET /api/test`);
  console.log(`  GET /api/market-data/prices`);
  console.log(`  GET /api/news/latest`);
  console.log(`  GET /api/analysis/indicators`);
  console.log(`====================================\n`);
});
