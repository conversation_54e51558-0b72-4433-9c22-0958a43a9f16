import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  firebaseUid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  preferences: {
    theme: 'light' | 'dark';
    defaultCurrency: string;
    notifications: {
      priceAlerts: boolean;
      news: boolean;
      economicEvents: boolean;
    };
    favoritesPairs: string[];
  };
  subscription: {
    plan: 'free' | 'premium';
    expiresAt?: Date;
  };
  lastLogin: Date;
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema = new Schema<IUser>({
  firebaseUid: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
  },
  displayName: String,
  photoURL: String,
  preferences: {
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'dark',
    },
    defaultCurrency: {
      type: String,
      default: 'USD',
    },
    notifications: {
      priceAlerts: { type: Boolean, default: true },
      news: { type: Boolean, default: true },
      economicEvents: { type: Boolean, default: true },
    },
    favoritesPairs: [{
      type: String,
      uppercase: true,
    }],
  },
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'premium'],
      default: 'free',
    },
    expiresAt: Date,
  },
  lastLogin: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
});

const User = mongoose.model<IUser>('User', UserSchema);
export { User };
export default User;
